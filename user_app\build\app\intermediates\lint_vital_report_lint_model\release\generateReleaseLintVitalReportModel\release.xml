<variant
    name="release"
    package="com.amalpoint.app"
    minSdkVersion="23"
    targetSdkVersion="35"
    shrinking="true"
    mergedManifest="C:\Users\<USER>\Desktop\amal_app\user_app\build\app\intermediates\merged_manifest\release\processReleaseMainManifest\AndroidManifest.xml"
    manifestMergeReport="C:\Users\<USER>\Desktop\amal_app\user_app\build\app\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="C:\Users\<USER>\Desktop\amal_app\user_app\build\app\intermediates\default_proguard_files\global\proguard-android.txt-8.7.2;C:\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro;proguard-rules.pro"
    partialResultsDir="C:\Users\<USER>\Desktop\amal_app\user_app\build\app\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out">
  <buildFeatures
      coreLibraryDesugaring="true"
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <artifact
      classOutputs="C:\Users\<USER>\Desktop\amal_app\user_app\build\app\intermediates\javac\release\compileReleaseJavaWithJavac\classes;C:\Users\<USER>\Desktop\amal_app\user_app\build\app\tmp\kotlin-classes\release;C:\Users\<USER>\Desktop\amal_app\user_app\build\app\kotlinToolingMetadata;C:\Users\<USER>\Desktop\amal_app\user_app\build\app\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\processReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.amalpoint.app"
      generatedSourceFolders="C:\Users\<USER>\Desktop\amal_app\user_app\build\app\generated\ap_generated_sources\release\out"
      generatedResourceFolders="C:\Users\<USER>\Desktop\amal_app\user_app\build\app\generated\res\processReleaseGoogleServices;C:\Users\<USER>\Desktop\amal_app\user_app\build\app\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\89b82167f2d41008909359c909fe730f\transformed\desugar_jdk_libs_configuration-2.0.4-desugar-lint.txt;C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\9bc05a18517abf7607aee4f86e30471f\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
