{"logs": [{"outputFile": "com.amalpoint.app-mergeDebugResources-51:/values/values.xml", "map": [{"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\df4f1e929377aeab3067a875c625f8f3\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "363", "startColumns": "4", "startOffsets": "21697", "endColumns": "49", "endOffsets": "21742"}}, {"source": "C:\\Users\\<USER>\\Desktop\\amal_app\\user_app\\build\\app\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,199,281,385,494,614,736", "endColumns": "143,81,103,108,119,121,90", "endOffsets": "194,276,380,489,609,731,822"}, "to": {"startLines": "430,436,437,438,439,440,445", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "27383,27881,27963,28067,28176,28296,28602", "endColumns": "143,81,103,108,119,121,90", "endOffsets": "27522,27958,28062,28171,28291,28413,28688"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\12c5084d2eb6e209e8a5f2f0964b5df7\\transformed\\jetified-appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2269,2285,2291,3611,3627", "startColumns": "4,4,4,4,4", "startOffsets": "145468,145893,146071,191498,191909", "endLines": "2284,2290,2300,3626,3630", "endColumns": "24,24,24,24,24", "endOffsets": "145888,146066,146350,191904,192031"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\87d519c141b7a1c36f4df3a53f4e9634\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3603,3675,3747,3820,3877,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3670,3742,3815,3872,3930,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,124,125,126,127,129,130,131,132,133,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,231,232,236,237,238,239,240,241,242,272,273,274,275,276,277,278,279,315,316,317,318,323,331,332,337,359,365,366,368,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,446,451,452,453,454,455,456,464,465,469,473,477,482,488,495,499,503,508,512,516,520,524,528,532,538,542,548,552,558,562,567,571,574,578,584,588,594,598,604,607,611,615,619,623,627,628,629,630,633,636,639,642,646,647,648,649,650,653,655,657,659,664,665,669,675,679,680,682,693,694,698,704,708,709,710,714,741,745,746,750,778,948,974,1145,1171,1202,1210,1216,1230,1252,1257,1262,1272,1281,1290,1294,1301,1309,1316,1317,1326,1329,1332,1336,1340,1344,1347,1348,1353,1358,1368,1373,1380,1386,1387,1390,1394,1399,1401,1403,1406,1409,1411,1415,1418,1425,1428,1431,1435,1437,1441,1443,1445,1447,1451,1459,1467,1479,1485,1494,1497,1508,1511,1512,1517,1518,1547,1616,1686,1687,1697,1706,1858,1860,1864,1867,1870,1873,1876,1879,1882,1885,1889,1892,1895,1898,1902,1905,1909,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1935,1937,1938,1939,1940,1941,1942,1943,1944,1946,1947,1949,1950,1952,1954,1955,1957,1958,1959,1960,1961,1962,1964,1965,1966,1967,1968,1985,1987,1989,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2005,2006,2007,2008,2009,2010,2012,2016,2032,2033,2034,2035,2036,2037,2041,2042,2043,2044,2046,2048,2050,2052,2054,2055,2056,2057,2059,2061,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2077,2078,2079,2080,2082,2084,2085,2087,2088,2090,2092,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2105,2107,2108,2109,2110,2112,2113,2114,2115,2116,2118,2120,2122,2124,2125,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2144,2219,2222,2225,2228,2242,2259,2301,2330,2357,2366,2428,2792,2823,2961,3085,3109,3115,3144,3165,3289,3317,3323,3467,3493,3560,3631,3731,3751,3806,3818,3844", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2138,2273,2354,2415,2490,2566,2643,2881,2966,3048,3124,3200,3277,3355,3461,3567,3646,3975,4032,4892,4966,5041,5106,5172,5232,5293,5365,5438,5505,5573,5632,5691,5750,5809,5868,5922,5976,6029,6083,6137,6191,6535,6609,6688,6761,6906,6978,7050,7123,7180,7311,7385,7459,7534,7606,7679,7749,7820,7880,7941,8010,8079,8149,8223,8299,8363,8440,8516,8593,8658,8727,8804,8879,8948,9016,9093,9159,9220,9317,9382,9451,9550,9621,9680,9738,9795,9854,9918,9989,10061,10133,10205,10277,10344,10412,10480,10539,10602,10666,10756,10847,10907,10973,11040,11106,11176,11240,11293,11360,11421,11488,11601,11659,11722,11787,11852,11927,12000,12072,12121,12182,12243,12304,12366,12430,12494,12558,12623,12686,12746,12807,12873,12932,12992,13054,13125,13185,13884,13970,14220,14310,14397,14485,14567,14650,14740,16677,16729,16787,16832,16898,16962,17019,17076,19253,19310,19358,19407,19662,20032,20079,20337,21508,21811,21875,21997,22318,22392,22462,22540,22594,22664,22749,22797,22843,22904,22967,23033,23097,23168,23231,23296,23360,23421,23482,23534,23607,23681,23750,23825,23899,23973,24114,28693,29054,29132,29222,29310,29406,29496,30078,30167,30414,30695,30947,31232,31625,32102,32324,32546,32822,33049,33279,33509,33739,33969,34196,34615,34841,35266,35496,35924,36143,36426,36634,36765,36992,37418,37643,38070,38291,38716,38836,39112,39413,39737,40028,40342,40479,40610,40715,40957,41124,41328,41536,41807,41919,42031,42136,42253,42467,42613,42753,42839,43187,43275,43521,43939,44188,44270,44368,44960,45060,45312,45736,45991,46085,46174,46411,48435,48677,48779,49032,51188,61720,63236,73867,75395,77152,77778,78198,79259,80524,80780,81016,81563,82057,82662,82860,83440,84004,84379,84497,85035,85192,85388,85661,85917,86087,86228,86292,86657,87024,87700,87964,88302,88655,88749,88935,89241,89503,89628,89755,89994,90205,90324,90517,90694,91149,91330,91452,91711,91824,92011,92113,92220,92349,92624,93132,93628,94505,94799,95369,95518,96250,96422,96506,96842,96934,99000,104246,109635,109697,110275,110859,118806,118919,119148,119308,119460,119631,119797,119966,120133,120296,120539,120709,120882,121053,121327,121526,121731,122061,122145,122241,122337,122435,122535,122637,122739,122841,122943,123045,123145,123241,123353,123482,123605,123736,123867,123965,124079,124173,124313,124447,124543,124655,124755,124871,124967,125079,125179,125319,125455,125619,125749,125907,126057,126198,126342,126477,126589,126739,126867,126995,127131,127263,127393,127523,127635,128915,129061,129205,129343,129409,129499,129575,129679,129769,129871,129979,130087,130187,130267,130359,130457,130567,130645,130751,130843,130947,131057,131179,131342,132132,132212,132312,132402,132512,132602,132843,132937,133043,133135,133235,133347,133461,133577,133693,133787,133901,134013,134115,134235,134357,134439,134543,134663,134789,134887,134981,135069,135181,135297,135419,135531,135706,135822,135908,136000,136112,136236,136303,136429,136497,136625,136769,136897,136966,137061,137176,137289,137388,137497,137608,137719,137820,137925,138025,138155,138246,138369,138463,138575,138661,138765,138861,138949,139067,139171,139275,139401,139489,139597,139697,139787,139897,139981,140083,140167,140221,140285,140391,140477,140587,140671,141075,143691,143809,143924,144004,144365,144951,146355,147699,149060,149448,152223,162312,163352,170165,174466,175217,175479,176326,176705,180983,181837,182066,186674,187684,189636,192036,196160,196904,199035,199375,200686", "endLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,124,125,126,127,129,130,131,132,133,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,231,232,236,237,238,239,240,241,242,272,273,274,275,276,277,278,279,315,316,317,318,323,331,332,337,359,365,366,368,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,446,451,452,453,454,455,463,464,468,472,476,481,487,494,498,502,507,511,515,519,523,527,531,537,541,547,551,557,561,566,570,573,577,583,587,593,597,603,606,610,614,618,622,626,627,628,629,632,635,638,641,645,646,647,648,649,652,654,656,658,663,664,668,674,678,679,681,692,693,697,703,707,708,709,713,740,744,745,749,777,947,973,1144,1170,1201,1209,1215,1229,1251,1256,1261,1271,1280,1289,1293,1300,1308,1315,1316,1325,1328,1331,1335,1339,1343,1346,1347,1352,1357,1367,1372,1379,1385,1386,1389,1393,1398,1400,1402,1405,1408,1410,1414,1417,1424,1427,1430,1434,1436,1440,1442,1444,1446,1450,1458,1466,1478,1484,1493,1496,1507,1510,1511,1516,1517,1522,1615,1685,1686,1696,1705,1706,1859,1863,1866,1869,1872,1875,1878,1881,1884,1888,1891,1894,1897,1901,1904,1908,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1934,1936,1937,1938,1939,1940,1941,1942,1943,1945,1946,1948,1949,1951,1953,1954,1956,1957,1958,1959,1960,1961,1963,1964,1965,1966,1967,1968,1986,1988,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2004,2005,2006,2007,2008,2009,2011,2015,2019,2032,2033,2034,2035,2036,2040,2041,2042,2043,2045,2047,2049,2051,2053,2054,2055,2056,2058,2060,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2076,2077,2078,2079,2081,2083,2084,2086,2087,2089,2091,2093,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2106,2107,2108,2109,2111,2112,2113,2114,2115,2117,2119,2121,2123,2124,2125,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2218,2221,2224,2227,2241,2247,2268,2329,2356,2365,2427,2786,2795,2850,2978,3108,3114,3120,3164,3288,3308,3322,3326,3472,3527,3571,3696,3750,3805,3817,3843,3850", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "275,931,980,2019,2074,2133,2195,2349,2410,2485,2561,2638,2716,2961,3043,3119,3195,3272,3350,3456,3562,3641,3721,4027,4085,4961,5036,5101,5167,5227,5288,5360,5433,5500,5568,5627,5686,5745,5804,5863,5917,5971,6024,6078,6132,6186,6240,6604,6683,6756,6830,6973,7045,7118,7175,7233,7380,7454,7529,7601,7674,7744,7815,7875,7936,8005,8074,8144,8218,8294,8358,8435,8511,8588,8653,8722,8799,8874,8943,9011,9088,9154,9215,9312,9377,9446,9545,9616,9675,9733,9790,9849,9913,9984,10056,10128,10200,10272,10339,10407,10475,10534,10597,10661,10751,10842,10902,10968,11035,11101,11171,11235,11288,11355,11416,11483,11596,11654,11717,11782,11847,11922,11995,12067,12116,12177,12238,12299,12361,12425,12489,12553,12618,12681,12741,12802,12868,12927,12987,13049,13120,13180,13248,13965,14052,14305,14392,14480,14562,14645,14735,14826,16724,16782,16827,16893,16957,17014,17071,17125,19305,19353,19402,19453,19691,20074,20123,20378,21535,21870,21932,22049,22387,22457,22535,22589,22659,22744,22792,22838,22899,22962,23028,23092,23163,23226,23291,23355,23416,23477,23529,23602,23676,23745,23820,23894,23968,24109,24179,28741,29127,29217,29305,29401,29491,30073,30162,30409,30690,30942,31227,31620,32097,32319,32541,32817,33044,33274,33504,33734,33964,34191,34610,34836,35261,35491,35919,36138,36421,36629,36760,36987,37413,37638,38065,38286,38711,38831,39107,39408,39732,40023,40337,40474,40605,40710,40952,41119,41323,41531,41802,41914,42026,42131,42248,42462,42608,42748,42834,43182,43270,43516,43934,44183,44265,44363,44955,45055,45307,45731,45986,46080,46169,46406,48430,48672,48774,49027,51183,61715,63231,73862,75390,77147,77773,78193,79254,80519,80775,81011,81558,82052,82657,82855,83435,83999,84374,84492,85030,85187,85383,85656,85912,86082,86223,86287,86652,87019,87695,87959,88297,88650,88744,88930,89236,89498,89623,89750,89989,90200,90319,90512,90689,91144,91325,91447,91706,91819,92006,92108,92215,92344,92619,93127,93623,94500,94794,95364,95513,96245,96417,96501,96837,96929,97207,104241,109630,109692,110270,110854,110945,118914,119143,119303,119455,119626,119792,119961,120128,120291,120534,120704,120877,121048,121322,121521,121726,122056,122140,122236,122332,122430,122530,122632,122734,122836,122938,123040,123140,123236,123348,123477,123600,123731,123862,123960,124074,124168,124308,124442,124538,124650,124750,124866,124962,125074,125174,125314,125450,125614,125744,125902,126052,126193,126337,126472,126584,126734,126862,126990,127126,127258,127388,127518,127630,127770,129056,129200,129338,129404,129494,129570,129674,129764,129866,129974,130082,130182,130262,130354,130452,130562,130640,130746,130838,130942,131052,131174,131337,131494,132207,132307,132397,132507,132597,132838,132932,133038,133130,133230,133342,133456,133572,133688,133782,133896,134008,134110,134230,134352,134434,134538,134658,134784,134882,134976,135064,135176,135292,135414,135526,135701,135817,135903,135995,136107,136231,136298,136424,136492,136620,136764,136892,136961,137056,137171,137284,137383,137492,137603,137714,137815,137920,138020,138150,138241,138364,138458,138570,138656,138760,138856,138944,139062,139166,139270,139396,139484,139592,139692,139782,139892,139976,140078,140162,140216,140280,140386,140472,140582,140666,140786,143686,143804,143919,143999,144360,144593,145463,147694,149055,149443,152218,162122,162442,164704,170732,175212,175474,175674,176700,180978,181584,182061,182212,186884,188762,189943,195057,196899,199030,199370,200681,200884"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\c4b7ca6073ca4a730ec2a12d13ab7f10\\transformed\\jetified-core-common-2.0.3\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "2024", "startColumns": "4", "startOffsets": "131722", "endLines": "2031", "endColumns": "8", "endOffsets": "132127"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\d34f626455f78afcac948c3ed9fac44b\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "320,336,364,3037,3042", "startColumns": "4,4,4,4,4", "startOffsets": "19518,20272,21747,173298,173468", "endLines": "320,336,364,3041,3045", "endColumns": "56,64,63,24,24", "endOffsets": "19570,20332,21806,173463,173612"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\2589dc72bfe363753ff7e766baa135ab\\transformed\\jetified-play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "369,418", "startColumns": "4,4", "startOffsets": "22054,25984", "endColumns": "67,166", "endOffsets": "22117,26146"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\4a912f9377fc5aa082e9d42fc1878a4b\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,83", "endOffsets": "132,216"}, "to": {"startLines": "400,401", "startColumns": "4,4", "startOffsets": "24184,24266", "endColumns": "81,83", "endOffsets": "24261,24345"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\fbaedecc468214929cd2822c1ff81d18\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "29,70,71,88,89,120,121,224,225,226,227,228,229,230,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,325,326,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,372,403,404,405,406,407,408,409,447,1969,1970,1975,1978,1983,2139,2140,2796,2813,2983,3016,3046,3079", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2721,2793,4090,4155,6245,6314,13396,13466,13534,13606,13676,13737,13811,15054,15115,15176,15238,15302,15364,15425,15493,15593,15653,15719,15792,15861,15918,15970,17130,17202,17278,17343,17402,17461,17521,17581,17641,17701,17761,17821,17881,17941,18001,18061,18120,18180,18240,18300,18360,18420,18480,18540,18600,18660,18720,18779,18839,18899,18958,19017,19076,19135,19194,19762,19797,20383,20438,20501,20556,20614,20672,20733,20796,20853,20904,20954,21015,21072,21138,21172,21207,22248,24433,24500,24572,24641,24710,24784,24856,28746,127775,127892,128159,128452,128719,140791,140863,162447,163051,170886,172617,173617,174299", "endLines": "29,70,71,88,89,120,121,224,225,226,227,228,229,230,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,325,326,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,372,403,404,405,406,407,408,409,447,1969,1973,1975,1981,1983,2139,2140,2801,2822,3015,3036,3078,3084", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1040,2788,2876,4150,4216,6309,6372,13461,13529,13601,13671,13732,13806,13879,15110,15171,15233,15297,15359,15420,15488,15588,15648,15714,15787,15856,15913,15965,16027,17197,17273,17338,17397,17456,17516,17576,17636,17696,17756,17816,17876,17936,17996,18056,18115,18175,18235,18295,18355,18415,18475,18535,18595,18655,18715,18774,18834,18894,18953,19012,19071,19130,19189,19248,19792,19827,20433,20496,20551,20609,20667,20728,20791,20848,20899,20949,21010,21067,21133,21167,21202,21237,22313,24495,24567,24636,24705,24779,24851,24939,28812,127887,128088,128264,128648,128843,140858,140925,162645,163347,172612,173293,174294,174461"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\7b0f6745f3365da6ff3ad016a2511f6e\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "321,322,327,334,335,354,355,356,357,358", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "19575,19615,19832,20170,20225,21242,21296,21348,21397,21458", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "19610,19657,19870,20220,20267,21291,21343,21392,21453,21503"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\54d1c7f76ac1543cefe0e08e4f772234\\transformed\\jetified-firebase-messaging-24.1.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "435", "startColumns": "4", "startOffsets": "27799", "endColumns": "81", "endOffsets": "27876"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\8503e403a02a8e3286710ffa396f3f6a\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2141,2851,2857", "startColumns": "4,4,4,4", "startOffsets": "164,140930,164709,164920", "endLines": "3,2143,2856,2940", "endColumns": "60,12,24,24", "endOffsets": "220,141070,164915,169431"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\041d9c99853163f266c54233b9e346d6\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "63,123,261,262,263,264,265,266,267,328,329,330,370,371,428,431,441,442,448,449,450,1523,1707,1710,1716,1722,1725,1731,1735,1738,1745,1751,1754,1760,1765,1770,1777,1779,1785,1791,1799,1804,1811,1816,1822,1826,1833,1837,1843,1849,1852,1856,1857,2787,2802,2941,2979,3121,3309,3327,3391,3401,3411,3418,3424,3528,3697,3714", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2200,6466,16032,16096,16151,16219,16286,16351,16408,19875,19923,19971,22122,22185,27277,27527,28418,28462,28817,28956,29006,97212,110950,111055,111300,111638,111784,112124,112336,112499,112906,113244,113367,113706,113945,114202,114573,114633,114971,115257,115706,115998,116386,116691,117035,117280,117610,117817,118085,118358,118502,118703,118750,162127,162650,169436,170737,175679,181589,182217,184142,184424,184729,184991,185251,188767,195062,195592", "endLines": "63,123,261,262,263,264,265,266,267,328,329,330,370,371,428,431,441,444,448,449,450,1539,1709,1715,1721,1724,1730,1734,1737,1744,1750,1753,1759,1764,1769,1776,1778,1784,1790,1798,1803,1810,1815,1821,1825,1832,1836,1842,1848,1851,1855,1856,1857,2791,2812,2960,2982,3130,3316,3390,3400,3410,3417,3423,3466,3540,3713,3730", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2268,6530,16091,16146,16214,16281,16346,16403,16460,19918,19966,20027,22180,22243,27310,27579,28457,28597,28951,29001,29049,98645,111050,111295,111633,111779,112119,112331,112494,112901,113239,113362,113701,113940,114197,114568,114628,114966,115252,115701,115993,116381,116686,117030,117275,117605,117812,118080,118353,118497,118698,118745,118801,162307,163046,170160,170881,176006,181832,184137,184419,184724,184986,185246,186669,189214,195587,196155"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\009dcbe301d232d3b1011f2afe616a66\\transformed\\jetified-activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "333,361", "startColumns": "4,4", "startOffsets": "20128,21583", "endColumns": "41,59", "endOffsets": "20165,21638"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\c0e6413d01cac97b19f7e6a014a571b6\\transformed\\media-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,341,394,447,500,560,626,748,809,875", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "139,210,283,336,389,442,495,555,621,743,804,870,937"}, "to": {"startLines": "122,128,134,268,269,270,271,367,1974,1976,1977,1982,1984", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6377,6835,7238,16465,16518,16571,16624,21937,128093,128269,128391,128653,128848", "endColumns": "88,70,72,52,52,52,52,59,65,121,60,65,66", "endOffsets": "6461,6901,7306,16513,16566,16619,16672,21992,128154,128386,128447,128714,128910"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\ff5c36b06dedfe2a8bc1119e56174554\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "402", "startColumns": "4", "startOffsets": "24350", "endColumns": "82", "endOffsets": "24428"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\37fa4adf7761f912182f68c7ab5f9e09\\transformed\\jetified-credentials-play-services-auth-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "273"}, "to": {"startLines": "2020", "startColumns": "4", "startOffsets": "131499", "endLines": "2023", "endColumns": "12", "endOffsets": "131717"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\3650d6cc361da30adc36c04cca2369d4\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "362", "startColumns": "4", "startOffsets": "21643", "endColumns": "53", "endOffsets": "21692"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\e11b6da5f28ffd75bf66664d467589a8\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "82,83,84,85,222,223,429,432,433,434", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3726,3784,3850,3913,13253,13324,27315,27584,27651,27730", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "3779,3845,3908,3970,13319,13391,27378,27646,27725,27794"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\e15ee9b978911c075688ce6d8881a3da\\transformed\\jetified-play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "90,91,92,93,94,95,96,97,410,411,412,413,414,415,416,417,419,420,421,422,423,424,425,426,427,3131,3541", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4221,4311,4391,4481,4571,4651,4732,4812,24944,25049,25230,25355,25462,25642,25765,25881,26151,26339,26444,26625,26750,26925,27073,27136,27198,176011,189219", "endLines": "90,91,92,93,94,95,96,97,410,411,412,413,414,415,416,417,419,420,421,422,423,424,425,426,427,3143,3559", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4306,4386,4476,4566,4646,4727,4807,4887,25044,25225,25350,25457,25637,25760,25876,25979,26334,26439,26620,26745,26920,27068,27131,27193,27272,176321,189631"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\8a69b41dc6c78bc132965cab6fd84925\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,319,2248,2254,3572,3580,3595", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,19458,144598,144793,189948,190230,190844", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,319,2253,2258,3579,3594,3610", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,19513,144788,144946,190225,190839,191493"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\2b51ceadabbe1b4cae426fad56aee009\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "360", "startColumns": "4", "startOffsets": "21540", "endColumns": "42", "endOffsets": "21578"}}, {"source": "C:\\Users\\<USER>\\Desktop\\amal_app\\user_app\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1540,1544", "startColumns": "4,4", "startOffsets": "98650,98831", "endLines": "1543,1546", "endColumns": "12,12", "endOffsets": "98826,98995"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\c992c2a2f371a25d051cb4152bfe134a\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "233,234,235,243,244,245,324,3473", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "14057,14116,14164,14831,14906,14982,19696,186889", "endLines": "233,234,235,243,244,245,324,3492", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "14111,14159,14215,14901,14977,15049,19757,187679"}}]}]}