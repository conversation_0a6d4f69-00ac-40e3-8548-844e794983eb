<variant
    name="release"
    package="io.flutter.plugins.imagepicker"
    minSdkVersion="19"
    targetSdkVersion="19"
    mergedManifest="C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml"
    manifestMergeReport="C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\default_proguard_files\global\proguard-android.txt-8.7.2"
    partialResultsDir="C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\javac\release\compileReleaseJavaWithJavac\classes;C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="io.flutter.plugins.imagepicker"
      generatedSourceFolders="C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\generated\ap_generated_sources\release\out"
      generatedResourceFolders="C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e9b7cfdf1ee989bd3f1dbc27a80781f4\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
