<variant
    name="release"
    package="io.flutter.plugins.firebase.firestore"
    minSdkVersion="21"
    targetSdkVersion="21"
    mergedManifest="C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml"
    manifestMergeReport="C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\intermediates\default_proguard_files\global\proguard-android.txt-8.7.2"
    partialResultsDir="C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\intermediates\javac\release\compileReleaseJavaWithJavac\classes;C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="io.flutter.plugins.firebase.firestore"
      generatedSourceFolders="C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\generated\ap_generated_sources\release\out;C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\generated\source\buildConfig\release"
      generatedResourceFolders="C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\25c5eef0e64ec4336fa331afcd167f44\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
