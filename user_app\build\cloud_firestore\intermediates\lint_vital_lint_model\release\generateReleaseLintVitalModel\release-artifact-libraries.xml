<libraries>
  <library
      name=":@@:firebase_core::release"
      project=":firebase_core"/>
  <library
      name="com.google.firebase:firebase-firestore:25.1.4@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0d970bbbd29c11fbfe796a8a2579f275\transformed\jetified-firebase-firestore-25.1.4\jars\classes.jar"
      resolved="com.google.firebase:firebase-firestore:25.1.4"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0d970bbbd29c11fbfe796a8a2579f275\transformed\jetified-firebase-firestore-25.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.flutter:flutter_embedding_release:1.0.0-36335019a8eab588c3c2ea783c618d90505be233@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\io.flutter\flutter_embedding_release\1.0.0-36335019a8eab588c3c2ea783c618d90505be233\28bfbfaa463acde1b8146ab5ad96c7eff77ebd97\flutter_embedding_release-1.0.0-36335019a8eab588c3c2ea783c618d90505be233.jar"
      resolved="io.flutter:flutter_embedding_release:1.0.0-36335019a8eab588c3c2ea783c618d90505be233"/>
  <library
      name="com.google.firebase:protolite-well-known-types:18.0.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\173eb89321ecfe81944086b5a743884c\transformed\jetified-protolite-well-known-types-18.0.1\jars\classes.jar"
      resolved="com.google.firebase:protolite-well-known-types:18.0.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\173eb89321ecfe81944086b5a743884c\transformed\jetified-protolite-well-known-types-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-appcheck-interop:17.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\dcc437978a2764380b6c3fde30a7cde0\transformed\jetified-firebase-appcheck-interop-17.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-appcheck-interop:17.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\dcc437978a2764380b6c3fde30a7cde0\transformed\jetified-firebase-appcheck-interop-17.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-auth-interop:19.0.2@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b1debe35817c91ecd1f2e7b62a31e9be\transformed\jetified-firebase-auth-interop-19.0.2\jars\classes.jar"
      resolved="com.google.firebase:firebase-auth-interop:19.0.2"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b1debe35817c91ecd1f2e7b62a31e9be\transformed\jetified-firebase-auth-interop-19.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\f097395e5cf189375fa05da693fd2e1f\transformed\jetified-firebase-common-ktx-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common-ktx:21.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\f097395e5cf189375fa05da693fd2e1f\transformed\jetified-firebase-common-ktx-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common:21.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common:21.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-database-collection:18.0.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\04ed46db81f5010929d06014123e1527\transformed\jetified-firebase-database-collection-18.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-database-collection:18.0.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\04ed46db81f5010929d06014123e1527\transformed\jetified-firebase-database-collection-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.1.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e15ee9b978911c075688ce6d8881a3da\transformed\jetified-play-services-base-18.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.1.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e15ee9b978911c075688ce6d8881a3da\transformed\jetified-play-services-base-18.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.3.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\dee6accbfa720b1633546095f71404e6\transformed\jetified-play-services-basement-18.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.3.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\dee6accbfa720b1633546095f71404e6\transformed\jetified-play-services-basement-18.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.7.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\d34f626455f78afcac948c3ed9fac44b\transformed\fragment-1.7.1\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.7.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\d34f626455f78afcac948c3ed9fac44b\transformed\fragment-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.8.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\82aa6fc6a3332a22c9264fa994666c08\transformed\jetified-activity-1.8.1\jars\classes.jar"
      resolved="androidx.activity:activity:1.8.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\82aa6fc6a3332a22c9264fa994666c08\transformed\jetified-activity-1.8.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0f483a269b96e2de7edcbbca391f9675\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0f483a269b96e2de7edcbbca391f9675\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\13663bbf632080e4b29f97e2afd80ee8\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\13663bbf632080e4b29f97e2afd80ee8\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e2d23ae6f4eaea7f3dbdaca5c2c7e372\transformed\lifecycle-livedata-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.7.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e2d23ae6f4eaea7f3dbdaca5c2c7e372\transformed\lifecycle-livedata-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\df4f1e929377aeab3067a875c625f8f3\transformed\lifecycle-viewmodel-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.7.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\df4f1e929377aeab3067a875c625f8f3\transformed\lifecycle-viewmodel-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a24bb4b2bea76a664eea4d57066019e3\transformed\lifecycle-livedata-core-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.7.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a24bb4b2bea76a664eea4d57066019e3\transformed\lifecycle-livedata-core-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c54bbe7f4b59a88be6bdce9a9057aadd\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c54bbe7f4b59a88be6bdce9a9057aadd\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\bf7caa4284b62c68329021149403836e\transformed\jetified-core-ktx-1.13.1\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\bf7caa4284b62c68329021149403836e\transformed\jetified-core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\9fced298559892d7c811c88aa8f25069\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\9fced298559892d7c811c88aa8f25069\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a922a660685c9d003ccf9f89ef454481\transformed\customview-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a922a660685c9d003ccf9f89ef454481\transformed\customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1\jars\classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2b51ceadabbe1b4cae426fad56aee009\transformed\lifecycle-runtime-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.7.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2b51ceadabbe1b4cae426fad56aee009\transformed\lifecycle-runtime-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.7.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.7.0\2ad14aed781c4a73ed4dbb421966d408a0a06686\lifecycle-common-java8-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.7.0"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.7.0\85334205d65cca70ed0109c3acbd29e22a2d9cb1\lifecycle-common-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.7.0"/>
  <library
      name="androidx.window:window:1.2.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0\jars\classes.jar"
      resolved="androidx.window:window:1.2.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window-java:1.2.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\9ca3b4b238c671b1b6ea51ff3a582142\transformed\jetified-window-java-1.2.0\jars\classes.jar"
      resolved="androidx.window:window-java:1.2.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\9ca3b4b238c671b1b6ea51ff3a582142\transformed\jetified-window-java-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-play-services\1.7.3\7087d47913cfb0062c9909dacbfc78fe44c5ecff\kotlinx-coroutines-play-services-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.1.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\1185d1a9cc95c80346c94d1bccf896af\transformed\jetified-play-services-tasks-18.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.1.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\1185d1a9cc95c80346c94d1bccf896af\transformed\jetified-play-services-tasks-18.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a745bebe6aa379a485423665720acdf3\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a745bebe6aa379a485423665720acdf3\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\androidx.collection\collection\1.1.0\1f27220b47669781457de0d600849a5de0e89909\collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2fb8a147a1c04b45f2b5d7295b92b8b5\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2fb8a147a1c04b45f2b5d7295b92b8b5\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\3650d6cc361da30adc36c04cca2369d4\transformed\jetified-savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\3650d6cc361da30adc36c04cca2369d4\transformed\jetified-savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.8.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.8.0\b8a16fe526014b7941c1debaccaf9c5153692dbb\annotation-jvm-1.8.0.jar"
      resolved="androidx.annotation:annotation-jvm:1.8.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.22\b25c86d47d6b962b9cf0f8c3f320c8a10eea3dd1\kotlin-stdlib-jdk8-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\1a0c197e98615fedc90a284f5a8bd3de\transformed\jetified-annotation-experimental-1.4.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\1a0c197e98615fedc90a284f5a8bd3de\transformed\jetified-annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.8.22\4dabb8248310d833bb6a8b516024a91fd3d275c\kotlin-stdlib-jdk7-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.8.22@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.8.22\636bf8b320e7627482771bbac9ed7246773c02bd\kotlin-stdlib-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-common:1.8.22@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-common\1.8.22\1a8e3601703ae14bb58757ea6b2d8e8e5935a586\kotlin-stdlib-common-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-common:1.8.22"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ff5c36b06dedfe2a8bc1119e56174554\transformed\jetified-startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ff5c36b06dedfe2a8bc1119e56174554\transformed\jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\488f3066395f3aa23529df9dfde68b96\transformed\jetified-tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\488f3066395f3aa23529df9dfde68b96\transformed\jetified-tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-components:18.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\341493ff67939c6b07505a1291e5dd9a\transformed\jetified-firebase-components-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-components:18.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\341493ff67939c6b07505a1291e5dd9a\transformed\jetified-firebase-components-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-annotations:16.2.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\com.google.firebase\firebase-annotations\16.2.0\ba0806703ca285d03fa9c888b5868f101134a501\firebase-annotations-16.2.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.2.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\d16c44b1768153149600cb80f691ab89\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\d16c44b1768153149600cb80f691ab89\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window.extensions.core:core:1.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4f80cba1b6927d4439c5d443256ac259\transformed\jetified-core-1.0.0\jars\classes.jar"
      resolved="androidx.window.extensions.core:core:1.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4f80cba1b6927d4439c5d443256ac259\transformed\jetified-core-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.grpc:grpc-android:1.62.2@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0cc0a1d33b69d349b3cc82fcb126506e\transformed\jetified-grpc-android-1.62.2\jars\classes.jar"
      resolved="io.grpc:grpc-android:1.62.2"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0cc0a1d33b69d349b3cc82fcb126506e\transformed\jetified-grpc-android-1.62.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.grpc:grpc-okhttp:1.62.2@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\io.grpc\grpc-okhttp\1.62.2\2d8b802e7fe17c1577527195fd53a2e7355b3541\grpc-okhttp-1.62.2.jar"
      resolved="io.grpc:grpc-okhttp:1.62.2"/>
  <library
      name="io.grpc:grpc-protobuf-lite:1.62.2@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\io.grpc\grpc-protobuf-lite\1.62.2\9d807d2a0e34bd7284a5336186f57cf241090920\grpc-protobuf-lite-1.62.2.jar"
      resolved="io.grpc:grpc-protobuf-lite:1.62.2"/>
  <library
      name="io.grpc:grpc-stub:1.62.2@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\io.grpc\grpc-stub\1.62.2\fc1e85697502d96d6c912e8dd2a56f46f1aba050\grpc-stub-1.62.2.jar"
      resolved="io.grpc:grpc-stub:1.62.2"/>
  <library
      name="com.squareup.okio:okio-jvm:3.4.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.4.0\4e8bd78a52ab935ce383d0092646922154295e54\okio-jvm-3.4.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.4.0"/>
  <library
      name="com.google.protobuf:protobuf-javalite:3.25.5@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\com.google.protobuf\protobuf-javalite\3.25.5\272641fe157ed7c4a22f8d4c347bcd7f6eac8887\protobuf-javalite-3.25.5.jar"
      resolved="com.google.protobuf:protobuf-javalite:3.25.5"/>
  <library
      name="io.grpc:grpc-util:1.62.2@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\io.grpc\grpc-util\1.62.2\98c4138f09fb57c3ad6cbeffb31ed73e302038f7\grpc-util-1.62.2.jar"
      resolved="io.grpc:grpc-util:1.62.2"/>
  <library
      name="io.grpc:grpc-core:1.62.2@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\io.grpc\grpc-core\1.62.2\5808049a5e33eba6f248a68d58e75399a68f2784\grpc-core-1.62.2.jar"
      resolved="io.grpc:grpc-core:1.62.2"/>
  <library
      name="io.grpc:grpc-context:1.62.2@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\io.grpc\grpc-context\1.62.2\69e86c35140b3b1718d65635bb54ccecc4c12f14\grpc-context-1.62.2.jar"
      resolved="io.grpc:grpc-context:1.62.2"/>
  <library
      name="io.grpc:grpc-api:1.62.2@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\io.grpc\grpc-api\1.62.2\a93b6ee3761d48edd9a9279f20a58be1a245ad01\grpc-api-1.62.2.jar"
      resolved="io.grpc:grpc-api:1.62.2"/>
  <library
      name="com.google.guava:guava:32.1.3-android@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\com.google.guava\guava\32.1.3-android\ea090dd85ca2fa12d42d054369df888665230dd7\guava-32.1.3-android.jar"
      resolved="com.google.guava:guava:32.1.3-android"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.26.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.26.0\c513866fd91bb46587500440a80fa943e95d12d9\error_prone_annotations-2.26.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.26.0"/>
  <library
      name="io.perfmark:perfmark-api:0.26.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\io.perfmark\perfmark-api\0.26.0\ef65452adaf20bf7d12ef55913aba24037b82738\perfmark-api-0.26.0.jar"
      resolved="io.perfmark:perfmark-api:0.26.0"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="com.google.code.gson:gson:2.10.1@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\com.google.code.gson\gson\2.10.1\b3add478d4382b78ea20b1671390a858002feb6c\gson-2.10.1.jar"
      resolved="com.google.code.gson:gson:2.10.1"/>
  <library
      name="com.google.android:annotations:4.1.1.4@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\com.google.android\annotations\4.1.1.4\a1678ba907bf92691d879fef34e1a187038f9259\annotations-4.1.1.4.jar"
      resolved="com.google.android:annotations:4.1.1.4"/>
  <library
      name="org.codehaus.mojo:animal-sniffer-annotations:1.23@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\org.codehaus.mojo\animal-sniffer-annotations\1.23\3c0daebd5f0e1ce72cc50c818321ac957aeb5d70\animal-sniffer-annotations-1.23.jar"
      resolved="org.codehaus.mojo:animal-sniffer-annotations:1.23"/>
  <library
      name="com.google.guava:failureaccess:1.0.1@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.1\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\failureaccess-1.0.1.jar"
      resolved="com.google.guava:failureaccess:1.0.1"/>
  <library
      name="org.checkerframework:checker-qual:3.37.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\org.checkerframework\checker-qual\3.37.0\ba74746d38026581c12166e164bb3c15e90cc4ea\checker-qual-3.37.0.jar"
      resolved="org.checkerframework:checker-qual:3.37.0"/>
</libraries>
