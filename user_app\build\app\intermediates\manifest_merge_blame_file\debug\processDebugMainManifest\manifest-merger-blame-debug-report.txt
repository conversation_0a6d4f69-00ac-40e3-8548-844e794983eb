1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.amalpoint.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:3:5-67
15-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:3:22-64
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Camera and storage permissions for image picker -->
16-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:4:5-79
16-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:4:22-76
17    <uses-permission android:name="android.permission.CAMERA" />
17-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:7:5-65
17-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:7:22-62
18    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:8:5-80
18-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:8:22-77
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:9:5-81
19-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:9:22-78
20    <!--
21 Required to query activities that can process text, see:
22         https://developer.android.com/training/package-visibility and
23         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
24
25         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
26    -->
27    <queries>
27-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:49:5-70:15
28        <intent>
28-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:50:9-53:18
29            <action android:name="android.intent.action.PROCESS_TEXT" />
29-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:51:13-72
29-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:51:21-70
30
31            <data android:mimeType="text/plain" />
31-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:52:13-50
31-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:52:19-48
32        </intent>
33        <!-- Query for WhatsApp -->
34        <package android:name="com.whatsapp" />
34-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:55:9-48
34-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:55:18-45
35        <package android:name="com.whatsapp.w4b" />
35-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:56:9-52
35-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:56:18-49
36        <!-- Query for WeChat -->
37        <package android:name="com.tencent.mm" />
37-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:58:9-50
37-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:58:18-47
38        <!-- Query for web browsers -->
39        <intent>
39-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:60:9-64:18
40            <action android:name="android.intent.action.VIEW" />
40-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:13-65
40-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:21-62
41
42            <category android:name="android.intent.category.BROWSABLE" />
42-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:13-74
42-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:23-71
43
44            <data android:scheme="https" />
44-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:52:13-50
45        </intent>
46        <intent>
46-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:65:9-69:18
47            <action android:name="android.intent.action.VIEW" />
47-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:13-65
47-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:21-62
48
49            <category android:name="android.intent.category.BROWSABLE" />
49-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:13-74
49-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:23-71
50
51            <data android:scheme="http" />
51-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:52:13-50
52        </intent>
53    </queries>
54
55    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Permissions options for the `notification` group -->
55-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
55-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
56    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
56-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
56-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:22-74
57    <uses-permission android:name="android.permission.VIBRATE" /> <!-- Required by older versions of Google Play services to create IID tokens -->
57-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\amal_app\user_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
57-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\amal_app\user_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
58    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
58-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
58-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:22-79
59    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
59-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\772d5e5ffa60b06272bcc2af1d02beb4\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
59-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\772d5e5ffa60b06272bcc2af1d02beb4\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
60
61    <permission
61-->[androidx.core:core:1.13.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
62        android:name="com.amalpoint.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
62-->[androidx.core:core:1.13.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
63        android:protectionLevel="signature" />
63-->[androidx.core:core:1.13.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
64
65    <uses-permission android:name="com.amalpoint.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
65-->[androidx.core:core:1.13.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
65-->[androidx.core:core:1.13.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
66
67    <application
68        android:name="android.app.Application"
69        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
69-->[androidx.core:core:1.13.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
70        android:debuggable="true"
71        android:extractNativeLibs="false"
72        android:icon="@mipmap/ic_launcher"
73        android:label="Amal Point"
74        android:testOnly="true"
75        android:usesCleartextTraffic="true" >
76        <activity
77            android:name="com.amalpoint.app.MainActivity"
78            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
79            android:exported="true"
80            android:hardwareAccelerated="true"
81            android:launchMode="singleTop"
82            android:taskAffinity=""
83            android:theme="@style/LaunchTheme"
84            android:windowSoftInputMode="adjustResize" >
85
86            <!--
87                 Specifies an Android theme to apply to this Activity as soon as
88                 the Android process has started. This theme is visible to the user
89                 while the Flutter UI initializes. After that, this theme continues
90                 to determine the Window background behind the Flutter UI.
91            -->
92            <meta-data
93                android:name="io.flutter.embedding.android.NormalTheme"
94                android:resource="@style/NormalTheme" />
95
96            <intent-filter>
97                <action android:name="android.intent.action.MAIN" />
98
99                <category android:name="android.intent.category.LAUNCHER" />
100            </intent-filter>
101        </activity>
102        <!--
103             Don't delete the meta-data below.
104             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
105        -->
106        <meta-data
107            android:name="flutterEmbedding"
108            android:value="2" />
109
110        <service
110-->[:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
111            android:name="com.google.firebase.components.ComponentDiscoveryService"
111-->[:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
112            android:directBootAware="true"
112-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
113            android:exported="false" >
113-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
114            <meta-data
114-->[:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
115                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
115-->[:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-134
116                android:value="com.google.firebase.components.ComponentRegistrar" />
116-->[:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
117            <meta-data
117-->[:firebase_auth] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
118                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
118-->[:firebase_auth] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
119                android:value="com.google.firebase.components.ComponentRegistrar" />
119-->[:firebase_auth] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
120            <meta-data
120-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
121                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
121-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
122                android:value="com.google.firebase.components.ComponentRegistrar" />
122-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
123            <meta-data
123-->[:firebase_storage] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
124                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
124-->[:firebase_storage] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-126
125                android:value="com.google.firebase.components.ComponentRegistrar" />
125-->[:firebase_storage] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
126            <meta-data
126-->[:firebase_core] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
127                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
127-->[:firebase_core] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
128                android:value="com.google.firebase.components.ComponentRegistrar" />
128-->[:firebase_core] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
129            <meta-data
129-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
130                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
130-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
131                android:value="com.google.firebase.components.ComponentRegistrar" />
131-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
132            <meta-data
132-->[com.google.firebase:firebase-firestore:25.1.4] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0d970bbbd29c11fbfe796a8a2579f275\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
133                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
133-->[com.google.firebase:firebase-firestore:25.1.4] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0d970bbbd29c11fbfe796a8a2579f275\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
134                android:value="com.google.firebase.components.ComponentRegistrar" />
134-->[com.google.firebase:firebase-firestore:25.1.4] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0d970bbbd29c11fbfe796a8a2579f275\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
135            <meta-data
135-->[com.google.firebase:firebase-firestore:25.1.4] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0d970bbbd29c11fbfe796a8a2579f275\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
136                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
136-->[com.google.firebase:firebase-firestore:25.1.4] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0d970bbbd29c11fbfe796a8a2579f275\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
137                android:value="com.google.firebase.components.ComponentRegistrar" />
137-->[com.google.firebase:firebase-firestore:25.1.4] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0d970bbbd29c11fbfe796a8a2579f275\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
138            <meta-data
138-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
139                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
139-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
140                android:value="com.google.firebase.components.ComponentRegistrar" />
140-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
141            <meta-data
141-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
142                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
142-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
143                android:value="com.google.firebase.components.ComponentRegistrar" />
143-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
144            <meta-data
144-->[com.google.firebase:firebase-storage:21.0.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\79a40e6d32ee32ea382e80cc5731270a\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:30:13-32:85
145                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
145-->[com.google.firebase:firebase-storage:21.0.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\79a40e6d32ee32ea382e80cc5731270a\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:31:17-118
146                android:value="com.google.firebase.components.ComponentRegistrar" />
146-->[com.google.firebase:firebase-storage:21.0.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\79a40e6d32ee32ea382e80cc5731270a\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:32:17-82
147            <meta-data
147-->[com.google.firebase:firebase-storage:21.0.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\79a40e6d32ee32ea382e80cc5731270a\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:33:13-35:85
148                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
148-->[com.google.firebase:firebase-storage:21.0.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\79a40e6d32ee32ea382e80cc5731270a\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:34:17-107
149                android:value="com.google.firebase.components.ComponentRegistrar" />
149-->[com.google.firebase:firebase-storage:21.0.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\79a40e6d32ee32ea382e80cc5731270a\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:35:17-82
150            <meta-data
150-->[com.google.firebase:firebase-installations:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7ffa0f020cb523e981e833c868a033c\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
151                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
151-->[com.google.firebase:firebase-installations:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7ffa0f020cb523e981e833c868a033c\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
152                android:value="com.google.firebase.components.ComponentRegistrar" />
152-->[com.google.firebase:firebase-installations:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7ffa0f020cb523e981e833c868a033c\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
153            <meta-data
153-->[com.google.firebase:firebase-installations:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7ffa0f020cb523e981e833c868a033c\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
154                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
154-->[com.google.firebase:firebase-installations:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7ffa0f020cb523e981e833c868a033c\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
155                android:value="com.google.firebase.components.ComponentRegistrar" />
155-->[com.google.firebase:firebase-installations:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7ffa0f020cb523e981e833c868a033c\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
156            <meta-data
156-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\104f2f91932ef77d79f136343b083746\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
157                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
157-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\104f2f91932ef77d79f136343b083746\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
158                android:value="com.google.firebase.components.ComponentRegistrar" />
158-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\104f2f91932ef77d79f136343b083746\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
159            <meta-data
159-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\104f2f91932ef77d79f136343b083746\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
160                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
160-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\104f2f91932ef77d79f136343b083746\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
161                android:value="com.google.firebase.components.ComponentRegistrar" />
161-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\104f2f91932ef77d79f136343b083746\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
162            <meta-data
162-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\f097395e5cf189375fa05da693fd2e1f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
163                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
163-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\f097395e5cf189375fa05da693fd2e1f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
164                android:value="com.google.firebase.components.ComponentRegistrar" />
164-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\f097395e5cf189375fa05da693fd2e1f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
165            <meta-data
165-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
166                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
166-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
167                android:value="com.google.firebase.components.ComponentRegistrar" />
167-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
168            <meta-data
168-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fd608e4250006cc300b2e0a8781b817c\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
169                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
169-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fd608e4250006cc300b2e0a8781b817c\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
170                android:value="com.google.firebase.components.ComponentRegistrar" />
170-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fd608e4250006cc300b2e0a8781b817c\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
171        </service>
172        <service
172-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
173            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
173-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
174            android:exported="false"
174-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
175            android:permission="android.permission.BIND_JOB_SERVICE" />
175-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
176        <service
176-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
177            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
177-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
178            android:exported="false" >
178-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
179            <intent-filter>
179-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
180                <action android:name="com.google.firebase.MESSAGING_EVENT" />
180-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
180-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
181            </intent-filter>
182        </service>
183
184        <receiver
184-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
185            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
185-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
186            android:exported="true"
186-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
187            android:permission="com.google.android.c2dm.permission.SEND" >
187-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
188            <intent-filter>
188-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
189                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
189-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
189-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
190            </intent-filter>
191        </receiver>
192
193        <provider
193-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
194            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
194-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
195            android:authorities="com.amalpoint.app.flutterfirebasemessaginginitprovider"
195-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
196            android:exported="false"
196-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
197            android:initOrder="99" />
197-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
198        <provider
198-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
199            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
199-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
200            android:authorities="com.amalpoint.app.flutter.image_provider"
200-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
201            android:exported="false"
201-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
202            android:grantUriPermissions="true" >
202-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
203            <meta-data
203-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
204                android:name="android.support.FILE_PROVIDER_PATHS"
204-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
205                android:resource="@xml/flutter_image_picker_file_paths" />
205-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
206        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
207        <service
207-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
208            android:name="com.google.android.gms.metadata.ModuleDependencies"
208-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
209            android:enabled="false"
209-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
210            android:exported="false" >
210-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
211            <intent-filter>
211-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
212                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
212-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
212-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
213            </intent-filter>
214
215            <meta-data
215-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
216                android:name="photopicker_activity:0:required"
216-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
217                android:value="" />
217-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
218        </service>
219        <!--
220           Declares a provider which allows us to store files to share in
221           '.../caches/share_plus' and grant the receiving action access
222        -->
223        <provider
223-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
224            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
224-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
225            android:authorities="com.amalpoint.app.flutter.share_provider"
225-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
226            android:exported="false"
226-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
227            android:grantUriPermissions="true" >
227-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
228            <meta-data
228-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
229                android:name="android.support.FILE_PROVIDER_PATHS"
229-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
230                android:resource="@xml/flutter_share_file_paths" />
230-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
231        </provider>
232        <!--
233           This manifest declared broadcast receiver allows us to use an explicit
234           Intent when creating a PendingItent to be informed of the user's choice
235        -->
236        <receiver
236-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
237            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
237-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
238            android:exported="false" >
238-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
239            <intent-filter>
239-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
240                <action android:name="EXTRA_CHOSEN_COMPONENT" />
240-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
240-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
241            </intent-filter>
242        </receiver>
243
244        <activity
244-->[:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
245            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
245-->[:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
246            android:exported="false"
246-->[:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
247            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
247-->[:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
248        <activity
248-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
249            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
249-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
250            android:excludeFromRecents="true"
250-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
251            android:exported="true"
251-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
252            android:launchMode="singleTask"
252-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
253            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
253-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
254            <intent-filter>
254-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
255                <action android:name="android.intent.action.VIEW" />
255-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:13-65
255-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:21-62
256
257                <category android:name="android.intent.category.DEFAULT" />
257-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
257-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
258                <category android:name="android.intent.category.BROWSABLE" />
258-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:13-74
258-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:23-71
259
260                <data
260-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:52:13-50
261                    android:host="firebase.auth"
262                    android:path="/"
263                    android:scheme="genericidp" />
264            </intent-filter>
265        </activity>
266        <activity
266-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
267            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
267-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
268            android:excludeFromRecents="true"
268-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
269            android:exported="true"
269-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
270            android:launchMode="singleTask"
270-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
271            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
271-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
272            <intent-filter>
272-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
273                <action android:name="android.intent.action.VIEW" />
273-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:13-65
273-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:21-62
274
275                <category android:name="android.intent.category.DEFAULT" />
275-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
275-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
276                <category android:name="android.intent.category.BROWSABLE" />
276-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:13-74
276-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:23-71
277
278                <data
278-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:52:13-50
279                    android:host="firebase.auth"
280                    android:path="/"
281                    android:scheme="recaptcha" />
282            </intent-filter>
283        </activity>
284
285        <receiver
285-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
286            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
286-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
287            android:exported="true"
287-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
288            android:permission="com.google.android.c2dm.permission.SEND" >
288-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
289            <intent-filter>
289-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
290                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
290-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
290-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
291            </intent-filter>
292
293            <meta-data
293-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
294                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
294-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
295                android:value="true" />
295-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
296        </receiver>
297        <!--
298             FirebaseMessagingService performs security checks at runtime,
299             but set to not exported to explicitly avoid allowing another app to call it.
300        -->
301        <service
301-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
302            android:name="com.google.firebase.messaging.FirebaseMessagingService"
302-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
303            android:directBootAware="true"
303-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
304            android:exported="false" >
304-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
305            <intent-filter android:priority="-500" >
305-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
306                <action android:name="com.google.firebase.MESSAGING_EVENT" />
306-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
306-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
307            </intent-filter>
308        </service>
309
310        <provider
310-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
311            android:name="com.google.firebase.provider.FirebaseInitProvider"
311-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
312            android:authorities="com.amalpoint.app.firebaseinitprovider"
312-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
313            android:directBootAware="true"
313-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
314            android:exported="false"
314-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
315            android:initOrder="100" />
315-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
316
317        <service
317-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
318            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
318-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
319            android:enabled="true"
319-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
320            android:exported="false" >
320-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
321            <meta-data
321-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
322                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
322-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
323                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
323-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
324        </service>
325
326        <activity
326-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
327            android:name="androidx.credentials.playservices.HiddenActivity"
327-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
328            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
328-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
329            android:enabled="true"
329-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
330            android:exported="false"
330-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
331            android:fitsSystemWindows="true"
331-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
332            android:theme="@style/Theme.Hidden" >
332-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
333        </activity>
334        <activity
334-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
335            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
335-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
336            android:excludeFromRecents="true"
336-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
337            android:exported="false"
337-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
338            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
338-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
339        <!--
340            Service handling Google Sign-In user revocation. For apps that do not integrate with
341            Google Sign-In, this service will never be started.
342        -->
343        <service
343-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
344            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
344-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
345            android:exported="true"
345-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
346            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
346-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
347            android:visibleToInstantApps="true" />
347-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
348
349        <activity
349-->[com.google.android.gms:play-services-base:18.1.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e15ee9b978911c075688ce6d8881a3da\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
350            android:name="com.google.android.gms.common.api.GoogleApiActivity"
350-->[com.google.android.gms:play-services-base:18.1.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e15ee9b978911c075688ce6d8881a3da\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
351            android:exported="false"
351-->[com.google.android.gms:play-services-base:18.1.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e15ee9b978911c075688ce6d8881a3da\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
352            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
352-->[com.google.android.gms:play-services-base:18.1.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e15ee9b978911c075688ce6d8881a3da\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
353
354        <provider
354-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
355            android:name="androidx.startup.InitializationProvider"
355-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
356            android:authorities="com.amalpoint.app.androidx-startup"
356-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
357            android:exported="false" >
357-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
358            <meta-data
358-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
359                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
359-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
360                android:value="androidx.startup" />
360-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
361            <meta-data
361-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
362                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
362-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
363                android:value="androidx.startup" />
363-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
364        </provider>
365
366        <uses-library
366-->[androidx.window:window:1.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
367            android:name="androidx.window.extensions"
367-->[androidx.window:window:1.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
368            android:required="false" />
368-->[androidx.window:window:1.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
369        <uses-library
369-->[androidx.window:window:1.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
370            android:name="androidx.window.sidecar"
370-->[androidx.window:window:1.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
371            android:required="false" />
371-->[androidx.window:window:1.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
372
373        <meta-data
373-->[com.google.android.gms:play-services-basement:18.4.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2589dc72bfe363753ff7e766baa135ab\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
374            android:name="com.google.android.gms.version"
374-->[com.google.android.gms:play-services-basement:18.4.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2589dc72bfe363753ff7e766baa135ab\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
375            android:value="@integer/google_play_services_version" />
375-->[com.google.android.gms:play-services-basement:18.4.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2589dc72bfe363753ff7e766baa135ab\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
376
377        <receiver
377-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
378            android:name="androidx.profileinstaller.ProfileInstallReceiver"
378-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
379            android:directBootAware="false"
379-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
380            android:enabled="true"
380-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
381            android:exported="true"
381-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
382            android:permission="android.permission.DUMP" >
382-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
383            <intent-filter>
383-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
384                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
384-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
384-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
385            </intent-filter>
386            <intent-filter>
386-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
387                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
387-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
387-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
388            </intent-filter>
389            <intent-filter>
389-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
390                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
390-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
390-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
391            </intent-filter>
392            <intent-filter>
392-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
393                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
393-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
393-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
394            </intent-filter>
395        </receiver>
396
397        <service
397-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b69b1306b3326e265f938d9f0333ec1e\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
398            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
398-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b69b1306b3326e265f938d9f0333ec1e\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
399            android:exported="false" >
399-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b69b1306b3326e265f938d9f0333ec1e\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
400            <meta-data
400-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b69b1306b3326e265f938d9f0333ec1e\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
401                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
401-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b69b1306b3326e265f938d9f0333ec1e\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
402                android:value="cct" />
402-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b69b1306b3326e265f938d9f0333ec1e\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
403        </service>
404        <service
404-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a5d02eb0f48d1f354fd28ccb48986a7a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
405            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
405-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a5d02eb0f48d1f354fd28ccb48986a7a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
406            android:exported="false"
406-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a5d02eb0f48d1f354fd28ccb48986a7a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
407            android:permission="android.permission.BIND_JOB_SERVICE" >
407-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a5d02eb0f48d1f354fd28ccb48986a7a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
408        </service>
409
410        <receiver
410-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a5d02eb0f48d1f354fd28ccb48986a7a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
411            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
411-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a5d02eb0f48d1f354fd28ccb48986a7a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
412            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
412-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a5d02eb0f48d1f354fd28ccb48986a7a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
413        <activity
413-->[com.google.android.play:core-common:2.0.3] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c4b7ca6073ca4a730ec2a12d13ab7f10\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
414            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
414-->[com.google.android.play:core-common:2.0.3] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c4b7ca6073ca4a730ec2a12d13ab7f10\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
415            android:exported="false"
415-->[com.google.android.play:core-common:2.0.3] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c4b7ca6073ca4a730ec2a12d13ab7f10\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
416            android:stateNotNeeded="true"
416-->[com.google.android.play:core-common:2.0.3] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c4b7ca6073ca4a730ec2a12d13ab7f10\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
417            android:theme="@style/Theme.PlayCore.Transparent" />
417-->[com.google.android.play:core-common:2.0.3] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c4b7ca6073ca4a730ec2a12d13ab7f10\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
418    </application>
419
420</manifest>
