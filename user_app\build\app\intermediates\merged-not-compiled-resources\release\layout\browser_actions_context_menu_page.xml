<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright 2018 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License
-->

<androidx.browser.browseractions.BrowserActionsFallbackMenuView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/browser_actions_menu_view"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:minWidth="280dp"
    android:layout_gravity="center"
    android:background="@color/browser_actions_bg_grey">
    <TextView
        android:id="@+id/browser_actions_header_text"
        android:layout_marginBottom="10dp"
        android:layout_marginTop="10dp"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:maxLines="1"
        android:textSize="13sp"
        android:textStyle="bold"
        android:ellipsize="end"
        android:textColor="@color/browser_actions_title_color" />
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/browser_actions_divider_color" />
    <ListView
        android:id="@+id/browser_actions_menu_items"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:dividerHeight="0dp"
        android:divider="@null" />
</androidx.browser.browseractions.BrowserActionsFallbackMenuView>