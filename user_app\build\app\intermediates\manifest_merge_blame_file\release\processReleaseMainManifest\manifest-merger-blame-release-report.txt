1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.amalpoint.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10    <!-- Internet permission for Firebase and API calls -->
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:3:5-67
11-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:4:5-79
12-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:4:22-76
13
14    <!-- Camera and storage permissions for image picker -->
15    <uses-permission android:name="android.permission.CAMERA" />
15-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:7:5-65
15-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:7:22-62
16    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
16-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:8:5-80
16-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:8:22-77
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:9:5-81
17-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:9:22-78
18    <!--
19         Required to query activities that can process text, see:
20         https://developer.android.com/training/package-visibility and
21         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
22
23         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
24    -->
25    <queries>
25-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:49:5-70:15
26        <intent>
26-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:50:9-53:18
27            <action android:name="android.intent.action.PROCESS_TEXT" />
27-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:51:13-72
27-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:51:21-70
28
29            <data android:mimeType="text/plain" />
29-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:52:13-50
29-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:52:19-48
30        </intent>
31        <!-- Query for WhatsApp -->
32        <package android:name="com.whatsapp" />
32-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:55:9-48
32-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:55:18-45
33        <package android:name="com.whatsapp.w4b" />
33-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:56:9-52
33-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:56:18-49
34        <!-- Query for WeChat -->
35        <package android:name="com.tencent.mm" />
35-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:58:9-50
35-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:58:18-47
36        <!-- Query for web browsers -->
37        <intent>
37-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:60:9-64:18
38            <action android:name="android.intent.action.VIEW" />
38-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:13-65
38-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:21-62
39
40            <category android:name="android.intent.category.BROWSABLE" />
40-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:13-74
40-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:23-71
41
42            <data android:scheme="https" />
42-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:52:13-50
42-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:63:19-41
43        </intent>
44        <intent>
44-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:65:9-69:18
45            <action android:name="android.intent.action.VIEW" />
45-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:13-65
45-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:21-62
46
47            <category android:name="android.intent.category.BROWSABLE" />
47-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:13-74
47-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:23-71
48
49            <data android:scheme="http" />
49-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:52:13-50
49-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:63:19-41
50        </intent>
51    </queries>
52
53    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Permissions options for the `notification` group -->
53-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-68
53-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-65
54    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
54-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:5-77
54-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:22-74
55    <uses-permission android:name="android.permission.VIBRATE" /> <!-- Required by older versions of Google Play services to create IID tokens -->
55-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\amal_app\user_app\build\flutter_local_notifications\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-66
55-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\amal_app\user_app\build\flutter_local_notifications\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-63
56    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
56-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
56-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:22-79
57    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
57-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\772d5e5ffa60b06272bcc2af1d02beb4\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
57-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\772d5e5ffa60b06272bcc2af1d02beb4\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
58
59    <permission
59-->[androidx.core:core:1.13.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
60        android:name="com.amalpoint.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
60-->[androidx.core:core:1.13.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
61        android:protectionLevel="signature" />
61-->[androidx.core:core:1.13.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
62
63    <uses-permission android:name="com.amalpoint.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
63-->[androidx.core:core:1.13.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
63-->[androidx.core:core:1.13.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
64
65    <application
66        android:name="android.app.Application"
66-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:13:9-42
67        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
67-->[androidx.core:core:1.13.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
68        android:extractNativeLibs="false"
69        android:icon="@mipmap/ic_launcher"
69-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:14:9-43
70        android:label="Amal Point"
70-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:12:9-35
71        android:usesCleartextTraffic="true" >
71-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:15:9-44
72        <activity
72-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:16:9-37:20
73            android:name="com.amalpoint.app.MainActivity"
73-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:17:13-41
74            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
74-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:22:13-163
75            android:exported="true"
75-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:18:13-36
76            android:hardwareAccelerated="true"
76-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:23:13-47
77            android:launchMode="singleTop"
77-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:19:13-43
78            android:taskAffinity=""
78-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:20:13-36
79            android:theme="@style/LaunchTheme"
79-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:21:13-47
80            android:windowSoftInputMode="adjustResize" >
80-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:24:13-55
81
82            <!--
83                 Specifies an Android theme to apply to this Activity as soon as
84                 the Android process has started. This theme is visible to the user
85                 while the Flutter UI initializes. After that, this theme continues
86                 to determine the Window background behind the Flutter UI.
87            -->
88            <meta-data
88-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:29:13-32:17
89                android:name="io.flutter.embedding.android.NormalTheme"
89-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:30:15-70
90                android:resource="@style/NormalTheme" />
90-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:31:15-52
91
92            <intent-filter>
92-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:33:13-36:29
93                <action android:name="android.intent.action.MAIN" />
93-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:34:17-68
93-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:34:25-66
94
95                <category android:name="android.intent.category.LAUNCHER" />
95-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:35:17-76
95-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:35:27-74
96            </intent-filter>
97        </activity>
98        <!--
99             Don't delete the meta-data below.
100             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
101        -->
102        <meta-data
102-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:40:9-42:33
103            android:name="flutterEmbedding"
103-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:41:13-44
104            android:value="2" />
104-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:42:13-30
105
106        <service
106-->[:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:19
107            android:name="com.google.firebase.components.ComponentDiscoveryService"
107-->[:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:18-89
108            android:directBootAware="true"
108-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
109            android:exported="false" >
109-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
110            <meta-data
110-->[:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
111                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
111-->[:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-134
112                android:value="com.google.firebase.components.ComponentRegistrar" />
112-->[:cloud_firestore] C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
113            <meta-data
113-->[:firebase_auth] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_auth\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
114                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
114-->[:firebase_auth] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_auth\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-124
115                android:value="com.google.firebase.components.ComponentRegistrar" />
115-->[:firebase_auth] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_auth\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
116            <meta-data
116-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:36:13-38:85
117                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
117-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:17-128
118                android:value="com.google.firebase.components.ComponentRegistrar" />
118-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:17-82
119            <meta-data
119-->[:firebase_storage] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_storage\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
120                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
120-->[:firebase_storage] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_storage\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-126
121                android:value="com.google.firebase.components.ComponentRegistrar" />
121-->[:firebase_storage] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_storage\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
122            <meta-data
122-->[:firebase_core] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_core\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
123                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
123-->[:firebase_core] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_core\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-124
124                android:value="com.google.firebase.components.ComponentRegistrar" />
124-->[:firebase_core] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_core\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
125            <meta-data
125-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
126                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
126-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
127                android:value="com.google.firebase.components.ComponentRegistrar" />
127-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
128            <meta-data
128-->[com.google.firebase:firebase-firestore:25.1.4] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0d970bbbd29c11fbfe796a8a2579f275\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
129                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
129-->[com.google.firebase:firebase-firestore:25.1.4] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0d970bbbd29c11fbfe796a8a2579f275\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
130                android:value="com.google.firebase.components.ComponentRegistrar" />
130-->[com.google.firebase:firebase-firestore:25.1.4] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0d970bbbd29c11fbfe796a8a2579f275\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
131            <meta-data
131-->[com.google.firebase:firebase-firestore:25.1.4] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0d970bbbd29c11fbfe796a8a2579f275\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
132                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
132-->[com.google.firebase:firebase-firestore:25.1.4] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0d970bbbd29c11fbfe796a8a2579f275\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
133                android:value="com.google.firebase.components.ComponentRegistrar" />
133-->[com.google.firebase:firebase-firestore:25.1.4] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0d970bbbd29c11fbfe796a8a2579f275\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
134            <meta-data
134-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
135                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
135-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
136                android:value="com.google.firebase.components.ComponentRegistrar" />
136-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
137            <meta-data
137-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
138                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
138-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
139                android:value="com.google.firebase.components.ComponentRegistrar" />
139-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
140            <meta-data
140-->[com.google.firebase:firebase-storage:21.0.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\79a40e6d32ee32ea382e80cc5731270a\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:30:13-32:85
141                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
141-->[com.google.firebase:firebase-storage:21.0.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\79a40e6d32ee32ea382e80cc5731270a\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:31:17-118
142                android:value="com.google.firebase.components.ComponentRegistrar" />
142-->[com.google.firebase:firebase-storage:21.0.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\79a40e6d32ee32ea382e80cc5731270a\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:32:17-82
143            <meta-data
143-->[com.google.firebase:firebase-storage:21.0.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\79a40e6d32ee32ea382e80cc5731270a\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:33:13-35:85
144                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
144-->[com.google.firebase:firebase-storage:21.0.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\79a40e6d32ee32ea382e80cc5731270a\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:34:17-107
145                android:value="com.google.firebase.components.ComponentRegistrar" />
145-->[com.google.firebase:firebase-storage:21.0.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\79a40e6d32ee32ea382e80cc5731270a\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:35:17-82
146            <meta-data
146-->[com.google.firebase:firebase-installations:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7ffa0f020cb523e981e833c868a033c\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
147                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
147-->[com.google.firebase:firebase-installations:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7ffa0f020cb523e981e833c868a033c\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
148                android:value="com.google.firebase.components.ComponentRegistrar" />
148-->[com.google.firebase:firebase-installations:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7ffa0f020cb523e981e833c868a033c\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
149            <meta-data
149-->[com.google.firebase:firebase-installations:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7ffa0f020cb523e981e833c868a033c\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
150                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
150-->[com.google.firebase:firebase-installations:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7ffa0f020cb523e981e833c868a033c\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
151                android:value="com.google.firebase.components.ComponentRegistrar" />
151-->[com.google.firebase:firebase-installations:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7ffa0f020cb523e981e833c868a033c\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
152            <meta-data
152-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\104f2f91932ef77d79f136343b083746\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
153                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
153-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\104f2f91932ef77d79f136343b083746\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
154                android:value="com.google.firebase.components.ComponentRegistrar" />
154-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\104f2f91932ef77d79f136343b083746\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
155            <meta-data
155-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\104f2f91932ef77d79f136343b083746\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
156                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
156-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\104f2f91932ef77d79f136343b083746\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
157                android:value="com.google.firebase.components.ComponentRegistrar" />
157-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\104f2f91932ef77d79f136343b083746\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
158            <meta-data
158-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\f097395e5cf189375fa05da693fd2e1f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
159                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
159-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\f097395e5cf189375fa05da693fd2e1f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
160                android:value="com.google.firebase.components.ComponentRegistrar" />
160-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\f097395e5cf189375fa05da693fd2e1f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
161            <meta-data
161-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
162                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
162-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
163                android:value="com.google.firebase.components.ComponentRegistrar" />
163-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
164            <meta-data
164-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fd608e4250006cc300b2e0a8781b817c\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
165                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
165-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fd608e4250006cc300b2e0a8781b817c\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
166                android:value="com.google.firebase.components.ComponentRegistrar" />
166-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fd608e4250006cc300b2e0a8781b817c\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
167        </service>
168        <service
168-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:9-17:72
169            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
169-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-107
170            android:exported="false"
170-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-37
171            android:permission="android.permission.BIND_JOB_SERVICE" />
171-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-69
172        <service
172-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:9-24:19
173            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
173-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:13-97
174            android:exported="false" >
174-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-37
175            <intent-filter>
175-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-23:29
176                <action android:name="com.google.firebase.MESSAGING_EVENT" />
176-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:17-78
176-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:25-75
177            </intent-filter>
178        </service>
179
180        <receiver
180-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:9-33:20
181            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
181-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-98
182            android:exported="true"
182-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-36
183            android:permission="com.google.android.c2dm.permission.SEND" >
183-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-73
184            <intent-filter>
184-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-32:29
185                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
185-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:17-81
185-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:25-78
186            </intent-filter>
187        </receiver>
188
189        <provider
189-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:41:9-45:38
190            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
190-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:42:13-102
191            android:authorities="com.amalpoint.app.flutterfirebasemessaginginitprovider"
191-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:13-88
192            android:exported="false"
192-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-37
193            android:initOrder="99" />
193-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:45:13-35
194        <provider
194-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-17:20
195            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
195-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-82
196            android:authorities="com.amalpoint.app.flutter.image_provider"
196-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-74
197            android:exported="false"
197-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
198            android:grantUriPermissions="true" >
198-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-47
199            <meta-data
199-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:75
200                android:name="android.support.FILE_PROVIDER_PATHS"
200-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-67
201                android:resource="@xml/flutter_image_picker_file_paths" />
201-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:17-72
202        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
203        <service
203-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:19
204            android:name="com.google.android.gms.metadata.ModuleDependencies"
204-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-78
205            android:enabled="false"
205-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-36
206            android:exported="false" >
206-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
207            <intent-filter>
207-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-26:29
208                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
208-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-94
208-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-91
209            </intent-filter>
210
211            <meta-data
211-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-30:36
212                android:name="photopicker_activity:0:required"
212-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-63
213                android:value="" />
213-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-33
214        </service>
215        <!--
216           Declares a provider which allows us to store files to share in
217           '.../caches/share_plus' and grant the receiving action access
218        -->
219        <provider
219-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:9-21:20
220            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
220-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-77
221            android:authorities="com.amalpoint.app.flutter.share_provider"
221-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-74
222            android:exported="false"
222-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-37
223            android:grantUriPermissions="true" >
223-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-47
224            <meta-data
224-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:75
225                android:name="android.support.FILE_PROVIDER_PATHS"
225-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-67
226                android:resource="@xml/flutter_share_file_paths" />
226-->[:image_picker_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:17-72
227        </provider>
228        <!--
229           This manifest declared broadcast receiver allows us to use an explicit
230           Intent when creating a PendingItent to be informed of the user's choice
231        -->
232        <receiver
232-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:9-32:20
233            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
233-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-82
234            android:exported="false" >
234-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-37
235            <intent-filter>
235-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-31:29
236                <action android:name="EXTRA_CHOSEN_COMPONENT" />
236-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-65
236-->[:share_plus] C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:25-62
237            </intent-filter>
238        </receiver>
239
240        <activity
240-->[:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
241            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
241-->[:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
242            android:exported="false"
242-->[:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
243            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
243-->[:url_launcher_android] C:\Users\<USER>\Desktop\amal_app\user_app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
244        <activity
244-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
245            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
245-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
246            android:excludeFromRecents="true"
246-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
247            android:exported="true"
247-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
248            android:launchMode="singleTask"
248-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
249            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
249-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
250            <intent-filter>
250-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
251                <action android:name="android.intent.action.VIEW" />
251-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:13-65
251-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:21-62
252
253                <category android:name="android.intent.category.DEFAULT" />
253-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
253-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
254                <category android:name="android.intent.category.BROWSABLE" />
254-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:13-74
254-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:23-71
255
256                <data
256-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:52:13-50
257                    android:host="firebase.auth"
258                    android:path="/"
259                    android:scheme="genericidp" />
259-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:63:19-41
260            </intent-filter>
261        </activity>
262        <activity
262-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
263            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
263-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
264            android:excludeFromRecents="true"
264-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
265            android:exported="true"
265-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
266            android:launchMode="singleTask"
266-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
267            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
267-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
268            <intent-filter>
268-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
269                <action android:name="android.intent.action.VIEW" />
269-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:13-65
269-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:61:21-62
270
271                <category android:name="android.intent.category.DEFAULT" />
271-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
271-->[com.google.firebase:firebase-auth:23.2.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
272                <category android:name="android.intent.category.BROWSABLE" />
272-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:13-74
272-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:62:23-71
273
274                <data
274-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:52:13-50
275                    android:host="firebase.auth"
276                    android:path="/"
277                    android:scheme="recaptcha" />
277-->C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\AndroidManifest.xml:63:19-41
278            </intent-filter>
279        </activity>
280
281        <receiver
281-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
282            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
282-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
283            android:exported="true"
283-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
284            android:permission="com.google.android.c2dm.permission.SEND" >
284-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
285            <intent-filter>
285-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-32:29
286                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
286-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:17-81
286-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:25-78
287            </intent-filter>
288
289            <meta-data
289-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
290                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
290-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
291                android:value="true" />
291-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
292        </receiver>
293        <!--
294             FirebaseMessagingService performs security checks at runtime,
295             but set to not exported to explicitly avoid allowing another app to call it.
296        -->
297        <service
297-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
298            android:name="com.google.firebase.messaging.FirebaseMessagingService"
298-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
299            android:directBootAware="true"
299-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
300            android:exported="false" >
300-->[com.google.firebase:firebase-messaging:24.1.2] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
301            <intent-filter android:priority="-500" >
301-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-23:29
302                <action android:name="com.google.firebase.MESSAGING_EVENT" />
302-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:17-78
302-->[:firebase_messaging] C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:25-75
303            </intent-filter>
304        </service>
305
306        <provider
306-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
307            android:name="com.google.firebase.provider.FirebaseInitProvider"
307-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
308            android:authorities="com.amalpoint.app.firebaseinitprovider"
308-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
309            android:directBootAware="true"
309-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
310            android:exported="false"
310-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
311            android:initOrder="100" />
311-->[com.google.firebase:firebase-common:21.0.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
312
313        <service
313-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
314            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
314-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
315            android:enabled="true"
315-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
316            android:exported="false" >
316-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
317            <meta-data
317-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
318                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
318-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
319                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
319-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
320        </service>
321
322        <activity
322-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
323            android:name="androidx.credentials.playservices.HiddenActivity"
323-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
324            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
324-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
325            android:enabled="true"
325-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
326            android:exported="false"
326-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
327            android:fitsSystemWindows="true"
327-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
328            android:theme="@style/Theme.Hidden" >
328-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
329        </activity>
330        <activity
330-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
331            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
331-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
332            android:excludeFromRecents="true"
332-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
333            android:exported="false"
333-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
334            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
334-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
335        <!--
336            Service handling Google Sign-In user revocation. For apps that do not integrate with
337            Google Sign-In, this service will never be started.
338        -->
339        <service
339-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
340            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
340-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
341            android:exported="true"
341-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
342            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
342-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
343            android:visibleToInstantApps="true" />
343-->[com.google.android.gms:play-services-auth:20.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
344
345        <activity
345-->[com.google.android.gms:play-services-base:18.1.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e15ee9b978911c075688ce6d8881a3da\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
346            android:name="com.google.android.gms.common.api.GoogleApiActivity"
346-->[com.google.android.gms:play-services-base:18.1.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e15ee9b978911c075688ce6d8881a3da\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
347            android:exported="false"
347-->[com.google.android.gms:play-services-base:18.1.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e15ee9b978911c075688ce6d8881a3da\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
348            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
348-->[com.google.android.gms:play-services-base:18.1.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e15ee9b978911c075688ce6d8881a3da\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
349
350        <provider
350-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
351            android:name="androidx.startup.InitializationProvider"
351-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
352            android:authorities="com.amalpoint.app.androidx-startup"
352-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
353            android:exported="false" >
353-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
354            <meta-data
354-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
355                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
355-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
356                android:value="androidx.startup" />
356-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
357            <meta-data
357-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
358                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
358-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
359                android:value="androidx.startup" />
359-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
360        </provider>
361
362        <uses-library
362-->[androidx.window:window:1.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
363            android:name="androidx.window.extensions"
363-->[androidx.window:window:1.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
364            android:required="false" />
364-->[androidx.window:window:1.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
365        <uses-library
365-->[androidx.window:window:1.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
366            android:name="androidx.window.sidecar"
366-->[androidx.window:window:1.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
367            android:required="false" />
367-->[androidx.window:window:1.2.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
368
369        <meta-data
369-->[com.google.android.gms:play-services-basement:18.4.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2589dc72bfe363753ff7e766baa135ab\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
370            android:name="com.google.android.gms.version"
370-->[com.google.android.gms:play-services-basement:18.4.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2589dc72bfe363753ff7e766baa135ab\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
371            android:value="@integer/google_play_services_version" />
371-->[com.google.android.gms:play-services-basement:18.4.0] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2589dc72bfe363753ff7e766baa135ab\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
372
373        <receiver
373-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
374            android:name="androidx.profileinstaller.ProfileInstallReceiver"
374-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
375            android:directBootAware="false"
375-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
376            android:enabled="true"
376-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
377            android:exported="true"
377-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
378            android:permission="android.permission.DUMP" >
378-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
379            <intent-filter>
379-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
380                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
380-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
380-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
381            </intent-filter>
382            <intent-filter>
382-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
383                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
383-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
383-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
384            </intent-filter>
385            <intent-filter>
385-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
386                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
386-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
386-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
387            </intent-filter>
388            <intent-filter>
388-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
389                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
389-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
389-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
390            </intent-filter>
391        </receiver>
392
393        <service
393-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b69b1306b3326e265f938d9f0333ec1e\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
394            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
394-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b69b1306b3326e265f938d9f0333ec1e\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
395            android:exported="false" >
395-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b69b1306b3326e265f938d9f0333ec1e\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
396            <meta-data
396-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b69b1306b3326e265f938d9f0333ec1e\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
397                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
397-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b69b1306b3326e265f938d9f0333ec1e\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
398                android:value="cct" />
398-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b69b1306b3326e265f938d9f0333ec1e\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
399        </service>
400        <service
400-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a5d02eb0f48d1f354fd28ccb48986a7a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
401            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
401-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a5d02eb0f48d1f354fd28ccb48986a7a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
402            android:exported="false"
402-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a5d02eb0f48d1f354fd28ccb48986a7a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
403            android:permission="android.permission.BIND_JOB_SERVICE" >
403-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a5d02eb0f48d1f354fd28ccb48986a7a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
404        </service>
405
406        <receiver
406-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a5d02eb0f48d1f354fd28ccb48986a7a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
407            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
407-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a5d02eb0f48d1f354fd28ccb48986a7a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
408            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
408-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a5d02eb0f48d1f354fd28ccb48986a7a\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
409        <activity
409-->[com.google.android.play:core-common:2.0.3] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c4b7ca6073ca4a730ec2a12d13ab7f10\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
410            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
410-->[com.google.android.play:core-common:2.0.3] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c4b7ca6073ca4a730ec2a12d13ab7f10\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
411            android:exported="false"
411-->[com.google.android.play:core-common:2.0.3] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c4b7ca6073ca4a730ec2a12d13ab7f10\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
412            android:stateNotNeeded="true"
412-->[com.google.android.play:core-common:2.0.3] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c4b7ca6073ca4a730ec2a12d13ab7f10\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
413            android:theme="@style/Theme.PlayCore.Transparent" />
413-->[com.google.android.play:core-common:2.0.3] C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c4b7ca6073ca4a730ec2a12d13ab7f10\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
414    </application>
415
416</manifest>
