com.amalpoint.app-jetified-datastore-release-0 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\00083b04b38c3c766b03f27df1c82002\transformed\jetified-datastore-release\res
com.amalpoint.app-jetified-activity-1.9.3-1 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\009dcbe301d232d3b1011f2afe616a66\transformed\jetified-activity-1.9.3\res
com.amalpoint.app-preference-1.2.1-2 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\041d9c99853163f266c54233b9e346d6\transformed\preference-1.2.1\res
com.amalpoint.app-jetified-appcompat-resources-1.1.0-3 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\12c5084d2eb6e209e8a5f2f0964b5df7\transformed\jetified-appcompat-resources-1.1.0\res
com.amalpoint.app-jetified-lifecycle-livedata-core-ktx-2.7.0-4 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\13663bbf632080e4b29f97e2afd80ee8\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.amalpoint.app-jetified-annotation-experimental-1.4.0-5 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\1a0c197e98615fedc90a284f5a8bd3de\transformed\jetified-annotation-experimental-1.4.0\res
com.amalpoint.app-jetified-play-services-basement-18.4.0-6 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2589dc72bfe363753ff7e766baa135ab\transformed\jetified-play-services-basement-18.4.0\res
com.amalpoint.app-slidingpanelayout-1.2.0-7 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2ab365fbb5481069e49f2072c82c67b8\transformed\slidingpanelayout-1.2.0\res
com.amalpoint.app-lifecycle-runtime-2.7.0-8 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2b51ceadabbe1b4cae426fad56aee009\transformed\lifecycle-runtime-2.7.0\res
com.amalpoint.app-core-runtime-2.2.0-9 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2fb8a147a1c04b45f2b5d7295b92b8b5\transformed\core-runtime-2.2.0\res
com.amalpoint.app-jetified-savedstate-1.2.1-10 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\3650d6cc361da30adc36c04cca2369d4\transformed\jetified-savedstate-1.2.1\res
com.amalpoint.app-jetified-credentials-play-services-auth-1.2.0-rc01-11 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\res
com.amalpoint.app-jetified-tracing-1.2.0-12 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\488f3066395f3aa23529df9dfde68b96\transformed\jetified-tracing-1.2.0\res
com.amalpoint.app-jetified-credentials-1.2.0-rc01-13 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4a912f9377fc5aa082e9d42fc1878a4b\transformed\jetified-credentials-1.2.0-rc01\res
com.amalpoint.app-jetified-play-services-auth-20.7.0-14 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\res
com.amalpoint.app-jetified-core-1.0.0-15 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4f80cba1b6927d4439c5d443256ac259\transformed\jetified-core-1.0.0\res
com.amalpoint.app-jetified-firebase-messaging-24.1.2-16 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\res
com.amalpoint.app-jetified-lifecycle-viewmodel-ktx-2.7.0-17 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\665cb6e27f24f2f72f04c4f619ccce78\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.amalpoint.app-localbroadcastmanager-1.1.0-18 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6e0fe1122ed1d6fd2cab2fa53e61d9e3\transformed\localbroadcastmanager-1.1.0\res
com.amalpoint.app-transition-1.4.1-19 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\7b0f6745f3365da6ff3ad016a2511f6e\transformed\transition-1.4.1\res
com.amalpoint.app-jetified-lifecycle-runtime-ktx-2.7.0-20 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\7efe8f72f09e48886d417f2d2efe1d01\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.amalpoint.app-jetified-savedstate-ktx-1.2.1-21 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\7fef2f99aefab4e42c2471e8f1778935\transformed\jetified-savedstate-ktx-1.2.1\res
com.amalpoint.app-coordinatorlayout-1.0.0-22 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8503e403a02a8e3286710ffa396f3f6a\transformed\coordinatorlayout-1.0.0\res
com.amalpoint.app-appcompat-1.1.0-23 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\87d519c141b7a1c36f4df3a53f4e9634\transformed\appcompat-1.1.0\res
com.amalpoint.app-jetified-activity-ktx-1.9.3-24 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8864d15756d9956f0ea02681cd9b68e2\transformed\jetified-activity-ktx-1.9.3\res
com.amalpoint.app-jetified-window-1.2.0-25 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0\res
com.amalpoint.app-jetified-window-java-1.2.0-26 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\9ca3b4b238c671b1b6ea51ff3a582142\transformed\jetified-window-java-1.2.0\res
com.amalpoint.app-lifecycle-livedata-core-2.7.0-27 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a24bb4b2bea76a664eea4d57066019e3\transformed\lifecycle-livedata-core-2.7.0\res
com.amalpoint.app-jetified-firebase-common-21.0.0-28 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\res
com.amalpoint.app-jetified-core-ktx-1.13.1-29 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\bf7caa4284b62c68329021149403836e\transformed\jetified-core-ktx-1.13.1\res
com.amalpoint.app-jetified-lifecycle-process-2.7.0-30 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\res
com.amalpoint.app-media-1.1.0-31 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c0e6413d01cac97b19f7e6a014a571b6\transformed\media-1.1.0\res
com.amalpoint.app-jetified-core-common-2.0.3-32 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c4b7ca6073ca4a730ec2a12d13ab7f10\transformed\jetified-core-common-2.0.3\res
com.amalpoint.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-33 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c54bbe7f4b59a88be6bdce9a9057aadd\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.amalpoint.app-recyclerview-1.0.0-34 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c992c2a2f371a25d051cb4152bfe134a\transformed\recyclerview-1.0.0\res
com.amalpoint.app-fragment-1.7.1-35 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\d34f626455f78afcac948c3ed9fac44b\transformed\fragment-1.7.1\res
com.amalpoint.app-jetified-datastore-preferences-release-36 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\dcfcd8aecea5b6ce18a053dec9463281\transformed\jetified-datastore-preferences-release\res
com.amalpoint.app-lifecycle-viewmodel-2.7.0-37 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\df4f1e929377aeab3067a875c625f8f3\transformed\lifecycle-viewmodel-2.7.0\res
com.amalpoint.app-browser-1.8.0-38 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e11b6da5f28ffd75bf66664d467589a8\transformed\browser-1.8.0\res
com.amalpoint.app-jetified-play-services-base-18.1.0-39 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e15ee9b978911c075688ce6d8881a3da\transformed\jetified-play-services-base-18.1.0\res
com.amalpoint.app-lifecycle-livedata-2.7.0-40 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e2d23ae6f4eaea7f3dbdaca5c2c7e372\transformed\lifecycle-livedata-2.7.0\res
com.amalpoint.app-jetified-profileinstaller-1.3.1-41 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\res
com.amalpoint.app-jetified-fragment-ktx-1.7.1-42 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\f44ce256e9e41154b6e28ed0f8fc29c9\transformed\jetified-fragment-ktx-1.7.1\res
com.amalpoint.app-jetified-datastore-core-release-43 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\f608c969ffb0a31f9f611b357c09c200\transformed\jetified-datastore-core-release\res
com.amalpoint.app-core-1.13.1-44 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1\res
com.amalpoint.app-jetified-startup-runtime-1.1.1-45 C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ff5c36b06dedfe2a8bc1119e56174554\transformed\jetified-startup-runtime-1.1.1\res
com.amalpoint.app-main-46 C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\main\res
com.amalpoint.app-release-47 C:\Users\<USER>\Desktop\amal_app\user_app\android\app\src\release\res
com.amalpoint.app-pngs-48 C:\Users\<USER>\Desktop\amal_app\user_app\build\app\generated\res\pngs\release
com.amalpoint.app-res-49 C:\Users\<USER>\Desktop\amal_app\user_app\build\app\generated\res\processReleaseGoogleServices
com.amalpoint.app-resValues-50 C:\Users\<USER>\Desktop\amal_app\user_app\build\app\generated\res\resValues\release
com.amalpoint.app-packageReleaseResources-51 C:\Users\<USER>\Desktop\amal_app\user_app\build\app\intermediates\incremental\release\packageReleaseResources\merged.dir
com.amalpoint.app-packageReleaseResources-52 C:\Users\<USER>\Desktop\amal_app\user_app\build\app\intermediates\incremental\release\packageReleaseResources\stripped.dir
com.amalpoint.app-merged-not-compiled-resources-53 C:\Users\<USER>\Desktop\amal_app\user_app\build\app\intermediates\merged-not-compiled-resources\release
com.amalpoint.app-release-54 C:\Users\<USER>\Desktop\amal_app\user_app\build\app\intermediates\merged_res\release\mergeReleaseResources
com.amalpoint.app-release-55 C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore\intermediates\packaged_res\release\packageReleaseResources
com.amalpoint.app-release-56 C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_auth\intermediates\packaged_res\release\packageReleaseResources
com.amalpoint.app-release-57 C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_core\intermediates\packaged_res\release\packageReleaseResources
com.amalpoint.app-release-58 C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_messaging\intermediates\packaged_res\release\packageReleaseResources
com.amalpoint.app-release-59 C:\Users\<USER>\Desktop\amal_app\user_app\build\firebase_storage\intermediates\packaged_res\release\packageReleaseResources
com.amalpoint.app-release-60 C:\Users\<USER>\Desktop\amal_app\user_app\build\flutter_local_notifications\intermediates\packaged_res\release\packageReleaseResources
com.amalpoint.app-release-61 C:\Users\<USER>\Desktop\amal_app\user_app\build\flutter_plugin_android_lifecycle\intermediates\packaged_res\release\packageReleaseResources
com.amalpoint.app-release-62 C:\Users\<USER>\Desktop\amal_app\user_app\build\image_picker_android\intermediates\packaged_res\release\packageReleaseResources
com.amalpoint.app-release-63 C:\Users\<USER>\Desktop\amal_app\user_app\build\path_provider_android\intermediates\packaged_res\release\packageReleaseResources
com.amalpoint.app-release-64 C:\Users\<USER>\Desktop\amal_app\user_app\build\share_plus\intermediates\packaged_res\release\packageReleaseResources
com.amalpoint.app-release-65 C:\Users\<USER>\Desktop\amal_app\user_app\build\shared_preferences_android\intermediates\packaged_res\release\packageReleaseResources
com.amalpoint.app-release-66 C:\Users\<USER>\Desktop\amal_app\user_app\build\sqflite_android\intermediates\packaged_res\release\packageReleaseResources
com.amalpoint.app-release-67 C:\Users\<USER>\Desktop\amal_app\user_app\build\url_launcher_android\intermediates\packaged_res\release\packageReleaseResources
