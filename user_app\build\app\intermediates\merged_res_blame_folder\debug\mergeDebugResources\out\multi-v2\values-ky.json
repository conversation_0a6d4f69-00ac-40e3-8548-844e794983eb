{"logs": [{"outputFile": "com.amalpoint.app-mergeDebugResources-51:/values-ky/values-ky.xml", "map": [{"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\e11b6da5f28ffd75bf66664d467589a8\\transformed\\browser-1.8.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,154,259,368", "endColumns": "98,104,108,105", "endOffsets": "149,254,363,469"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6059,6250,6355,6464", "endColumns": "98,104,108,105", "endOffsets": "6153,6350,6459,6565"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\2589dc72bfe363753ff7e766baa135ab\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ky\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "153", "endOffsets": "348"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4771", "endColumns": "157", "endOffsets": "4924"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\fbaedecc468214929cd2822c1ff81d18\\transformed\\core-1.13.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,571,675,786", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "150,252,355,462,566,670,781,882"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3053,3153,3255,3358,3465,3569,3673,6871", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "3148,3250,3353,3460,3564,3668,3779,6967"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\e15ee9b978911c075688ce6d8881a3da\\transformed\\jetified-play-services-base-18.1.0\\res\\values-ky\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,440,565,668,809,932,1043,1148,1313,1416,1562,1688,1821,1981,2041,2097", "endColumns": "101,144,124,102,140,122,110,104,164,102,145,125,132,159,59,55,73", "endOffsets": "294,439,564,667,808,931,1042,1147,1312,1415,1561,1687,1820,1980,2040,2096,2170"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3784,3890,4039,4168,4275,4420,4547,4662,4929,5098,5205,5355,5485,5622,5786,5850,5910", "endColumns": "105,148,128,106,144,126,114,108,168,106,149,129,136,163,63,59,77", "endOffsets": "3885,4034,4163,4270,4415,4542,4657,4766,5093,5200,5350,5480,5617,5781,5845,5905,5983"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\041d9c99853163f266c54233b9e346d6\\transformed\\preference-1.2.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,268,347,488,657,738", "endColumns": "70,91,78,140,168,80,78", "endOffsets": "171,263,342,483,652,733,812"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5988,6158,6570,6649,6972,7141,7222", "endColumns": "70,91,78,140,168,80,78", "endOffsets": "6054,6245,6644,6785,7136,7217,7296"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\4a912f9377fc5aa082e9d42fc1878a4b\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,122", "endOffsets": "160,283"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2820,2930", "endColumns": "109,122", "endOffsets": "2925,3048"}}, {"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\87d519c141b7a1c36f4df3a53f4e9634\\transformed\\appcompat-1.1.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,902,993,1085,1180,1274,1375,1468,1563,1658,1749,1840,1920,2026,2131,2229,2336,2442,2557,2718,2820", "endColumns": "110,108,111,84,104,116,78,78,90,91,94,93,100,92,94,94,90,90,79,105,104,97,106,105,114,160,101,80", "endOffsets": "211,320,432,517,622,739,818,897,988,1080,1175,1269,1370,1463,1558,1653,1744,1835,1915,2021,2126,2224,2331,2437,2552,2713,2815,2896"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,902,993,1085,1180,1274,1375,1468,1563,1658,1749,1840,1920,2026,2131,2229,2336,2442,2557,2718,6790", "endColumns": "110,108,111,84,104,116,78,78,90,91,94,93,100,92,94,94,90,90,79,105,104,97,106,105,114,160,101,80", "endOffsets": "211,320,432,517,622,739,818,897,988,1080,1175,1269,1370,1463,1558,1653,1744,1835,1915,2021,2126,2224,2331,2437,2552,2713,2815,6866"}}]}]}