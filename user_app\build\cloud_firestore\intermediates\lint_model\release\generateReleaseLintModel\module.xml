<lint-module
    format="1"
    dir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-5.6.12\android"
    name=":cloud_firestore"
    type="LIBRARY"
    maven="io.flutter.plugins.firebase.cloudfirestore:cloud_firestore:1.0-SNAPSHOT"
    agpVersion="8.7.2"
    buildFolder="C:\Users\<USER>\Desktop\amal_app\user_app\build\cloud_firestore"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-34\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      disable="InvalidPackage"
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true">
    <severities>
      <severity
        id="InvalidPackage"
        severity="IGNORE" />
    </severities>
  </lintOptions>
  <variant name="release"/>
</lint-module>
