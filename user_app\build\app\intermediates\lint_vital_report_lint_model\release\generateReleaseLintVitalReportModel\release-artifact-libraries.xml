<libraries>
  <library
      name="__local_aars__:C:\Users\<USER>\Desktop\amal_app\user_app\build\app\intermediates\flutter\release\libs.jar:unspecified@jar"
      jars="C:\Users\<USER>\Desktop\amal_app\user_app\build\app\intermediates\flutter\release\libs.jar"
      resolved="__local_aars__:C:\Users\<USER>\Desktop\amal_app\user_app\build\app\intermediates\flutter\release\libs.jar:unspecified"/>
  <library
      name=":@@:cloud_firestore::release"
      project=":cloud_firestore"/>
  <library
      name=":@@:firebase_auth::release"
      project=":firebase_auth"/>
  <library
      name=":@@:firebase_messaging::release"
      project=":firebase_messaging"/>
  <library
      name=":@@:firebase_storage::release"
      project=":firebase_storage"/>
  <library
      name=":@@:firebase_core::release"
      project=":firebase_core"/>
  <library
      name=":@@:flutter_local_notifications::release"
      project=":flutter_local_notifications"/>
  <library
      name=":@@:flutter_plugin_android_lifecycle::release"
      project=":flutter_plugin_android_lifecycle"/>
  <library
      name=":@@:image_picker_android::release"
      project=":image_picker_android"/>
  <library
      name=":@@:path_provider_android::release"
      project=":path_provider_android"/>
  <library
      name=":@@:share_plus::release"
      project=":share_plus"/>
  <library
      name=":@@:shared_preferences_android::release"
      project=":shared_preferences_android"/>
  <library
      name=":@@:sqflite_android::release"
      project=":sqflite_android"/>
  <library
      name=":@@:url_launcher_android::release"
      project=":url_launcher_android"/>
  <library
      name="io.flutter:flutter_embedding_release:1.0.0-36335019a8eab588c3c2ea783c618d90505be233@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\io.flutter\flutter_embedding_release\1.0.0-36335019a8eab588c3c2ea783c618d90505be233\28bfbfaa463acde1b8146ab5ad96c7eff77ebd97\flutter_embedding_release-1.0.0-36335019a8eab588c3c2ea783c618d90505be233.jar"
      resolved="io.flutter:flutter_embedding_release:1.0.0-36335019a8eab588c3c2ea783c618d90505be233"/>
  <library
      name="androidx.fragment:fragment:1.7.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\d34f626455f78afcac948c3ed9fac44b\transformed\fragment-1.7.1\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.7.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\d34f626455f78afcac948c3ed9fac44b\transformed\fragment-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.9.3@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\009dcbe301d232d3b1011f2afe616a66\transformed\jetified-activity-1.9.3\jars\classes.jar"
      resolved="androidx.activity:activity:1.9.3"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\009dcbe301d232d3b1011f2afe616a66\transformed\jetified-activity-1.9.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0f483a269b96e2de7edcbbca391f9675\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0f483a269b96e2de7edcbbca391f9675\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\13663bbf632080e4b29f97e2afd80ee8\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\13663bbf632080e4b29f97e2afd80ee8\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e2d23ae6f4eaea7f3dbdaca5c2c7e372\transformed\lifecycle-livedata-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.7.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e2d23ae6f4eaea7f3dbdaca5c2c7e372\transformed\lifecycle-livedata-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\df4f1e929377aeab3067a875c625f8f3\transformed\lifecycle-viewmodel-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.7.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\df4f1e929377aeab3067a875c625f8f3\transformed\lifecycle-viewmodel-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a24bb4b2bea76a664eea4d57066019e3\transformed\lifecycle-livedata-core-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.7.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a24bb4b2bea76a664eea4d57066019e3\transformed\lifecycle-livedata-core-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c54bbe7f4b59a88be6bdce9a9057aadd\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c54bbe7f4b59a88be6bdce9a9057aadd\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\bf7caa4284b62c68329021149403836e\transformed\jetified-core-ktx-1.13.1\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\bf7caa4284b62c68329021149403836e\transformed\jetified-core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\9fced298559892d7c811c88aa8f25069\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\9fced298559892d7c811c88aa8f25069\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\eda37fd96a2edeed3cd8050571886319\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\eda37fd96a2edeed3cd8050571886319\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1\jars\classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fbaedecc468214929cd2822c1ff81d18\transformed\core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2b51ceadabbe1b4cae426fad56aee009\transformed\lifecycle-runtime-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.7.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2b51ceadabbe1b4cae426fad56aee009\transformed\lifecycle-runtime-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.7.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c00ef60e1e339fe57c08fe3226769f92\transformed\jetified-lifecycle-process-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.7.0\2ad14aed781c4a73ed4dbb421966d408a0a06686\lifecycle-common-java8-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.7.0"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.7.0\85334205d65cca70ed0109c3acbd29e22a2d9cb1\lifecycle-common-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.7.0"/>
  <library
      name="androidx.window:window:1.2.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0\jars\classes.jar"
      resolved="androidx.window:window:1.2.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8a69b41dc6c78bc132965cab6fd84925\transformed\jetified-window-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window-java:1.2.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\9ca3b4b238c671b1b6ea51ff3a582142\transformed\jetified-window-java-1.2.0\jars\classes.jar"
      resolved="androidx.window:window-java:1.2.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\9ca3b4b238c671b1b6ea51ff3a582142\transformed\jetified-window-java-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\1a0c197e98615fedc90a284f5a8bd3de\transformed\jetified-annotation-experimental-1.4.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\1a0c197e98615fedc90a284f5a8bd3de\transformed\jetified-annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\3650d6cc361da30adc36c04cca2369d4\transformed\jetified-savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\3650d6cc361da30adc36c04cca2369d4\transformed\jetified-savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2fb8a147a1c04b45f2b5d7295b92b8b5\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2fb8a147a1c04b45f2b5d7295b92b8b5\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a745bebe6aa379a485423665720acdf3\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a745bebe6aa379a485423665720acdf3\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\androidx.collection\collection\1.1.0\1f27220b47669781457de0d600849a5de0e89909\collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.9.1@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.9.1\b17951747e38bf3986a24431b9ba0d039958aa5f\annotation-jvm-1.9.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.9.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.22\b25c86d47d6b962b9cf0f8c3f320c8a10eea3dd1\kotlin-stdlib-jdk8-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.8.22\4dabb8248310d833bb6a8b516024a91fd3d275c\kotlin-stdlib-jdk7-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.9.25@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.25\f700a2f2b8f0d6d0fde48f56d894dc722fb029d7\kotlin-stdlib-1.9.25.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.9.25"/>
  <library
      name="androidx.multidex:multidex:2.0.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ec83e74c463208ceee9a0c5984f436f9\transformed\multidex-2.0.1\jars\classes.jar"
      resolved="androidx.multidex:multidex:2.0.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ec83e74c463208ceee9a0c5984f436f9\transformed\multidex-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.flutter:armeabi_v7a_release:1.0.0-36335019a8eab588c3c2ea783c618d90505be233@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\io.flutter\armeabi_v7a_release\1.0.0-36335019a8eab588c3c2ea783c618d90505be233\7b6809660ad92b20482314b428a29c3ea8851f3a\armeabi_v7a_release-1.0.0-36335019a8eab588c3c2ea783c618d90505be233.jar"
      resolved="io.flutter:armeabi_v7a_release:1.0.0-36335019a8eab588c3c2ea783c618d90505be233"/>
  <library
      name="io.flutter:arm64_v8a_release:1.0.0-36335019a8eab588c3c2ea783c618d90505be233@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\io.flutter\arm64_v8a_release\1.0.0-36335019a8eab588c3c2ea783c618d90505be233\4971457fbc45f8a135446348eca1b24599a0141b\arm64_v8a_release-1.0.0-36335019a8eab588c3c2ea783c618d90505be233.jar"
      resolved="io.flutter:arm64_v8a_release:1.0.0-36335019a8eab588c3c2ea783c618d90505be233"/>
  <library
      name="io.flutter:x86_64_release:1.0.0-36335019a8eab588c3c2ea783c618d90505be233@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\io.flutter\x86_64_release\1.0.0-36335019a8eab588c3c2ea783c618d90505be233\125056b87b256748feb11a54ad9c72e25dce9215\x86_64_release-1.0.0-36335019a8eab588c3c2ea783c618d90505be233.jar"
      resolved="io.flutter:x86_64_release:1.0.0-36335019a8eab588c3c2ea783c618d90505be233"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ff5c36b06dedfe2a8bc1119e56174554\transformed\jetified-startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ff5c36b06dedfe2a8bc1119e56174554\transformed\jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\488f3066395f3aa23529df9dfde68b96\transformed\jetified-tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\488f3066395f3aa23529df9dfde68b96\transformed\jetified-tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.1.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c0e6413d01cac97b19f7e6a014a571b6\transformed\media-1.1.0\jars\classes.jar"
      resolved="androidx.media:media:1.1.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c0e6413d01cac97b19f7e6a014a571b6\transformed\media-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-auth:23.2.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-auth:23.2.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6a6c98ce5431bbe2696e8679af828798\transformed\jetified-firebase-auth-23.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.preference:preference:1.2.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\041d9c99853163f266c54233b9e346d6\transformed\preference-1.2.1\jars\classes.jar"
      resolved="androidx.preference:preference:1.2.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\041d9c99853163f266c54233b9e346d6\transformed\preference-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.1.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\87d519c141b7a1c36f4df3a53f4e9634\transformed\appcompat-1.1.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.1.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\87d519c141b7a1c36f4df3a53f4e9634\transformed\appcompat-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-firestore:25.1.4@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0d970bbbd29c11fbfe796a8a2579f275\transformed\jetified-firebase-firestore-25.1.4\jars\classes.jar"
      resolved="com.google.firebase:firebase-firestore:25.1.4"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0d970bbbd29c11fbfe796a8a2579f275\transformed\jetified-firebase-firestore-25.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-messaging:24.1.2@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2\jars\classes.jar"
      resolved="com.google.firebase:firebase-messaging:24.1.2"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\54d1c7f76ac1543cefe0e08e4f772234\transformed\jetified-firebase-messaging-24.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-storage:21.0.2@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\79a40e6d32ee32ea382e80cc5731270a\transformed\jetified-firebase-storage-21.0.2\jars\classes.jar"
      resolved="com.google.firebase:firebase-storage:21.0.2"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\79a40e6d32ee32ea382e80cc5731270a\transformed\jetified-firebase-storage-21.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-auth-interop:20.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\1c30d9f74d40cd9cba2a010b9fca8d7e\transformed\jetified-firebase-auth-interop-20.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-auth-interop:20.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\1c30d9f74d40cd9cba2a010b9fca8d7e\transformed\jetified-firebase-auth-interop-20.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations:18.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7ffa0f020cb523e981e833c868a033c\transformed\jetified-firebase-installations-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations:18.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7ffa0f020cb523e981e833c868a033c\transformed\jetified-firebase-installations-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-appcheck:18.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\104f2f91932ef77d79f136343b083746\transformed\jetified-firebase-appcheck-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-appcheck:18.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\104f2f91932ef77d79f136343b083746\transformed\jetified-firebase-appcheck-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\f097395e5cf189375fa05da693fd2e1f\transformed\jetified-firebase-common-ktx-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common-ktx:21.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\f097395e5cf189375fa05da693fd2e1f\transformed\jetified-firebase-common-ktx-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common:21.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common:21.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b7b73daa65e65a86d17e94bc94150398\transformed\jetified-firebase-common-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.credentials:credentials:1.2.0-rc01@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4a912f9377fc5aa082e9d42fc1878a4b\transformed\jetified-credentials-1.2.0-rc01\jars\classes.jar"
      resolved="androidx.credentials:credentials:1.2.0-rc01"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4a912f9377fc5aa082e9d42fc1878a4b\transformed\jetified-credentials-1.2.0-rc01"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.credentials:credentials-play-services-auth:1.2.0-rc01@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\jars\classes.jar"
      resolved="androidx.credentials:credentials-play-services-auth:1.2.0-rc01"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\37fa4adf7761f912182f68c7ab5f9e09\transformed\jetified-credentials-play-services-auth-1.2.0-rc01"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth:20.7.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth:20.7.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4e82a87f483db2f5c6cc734d8f8efaf3\transformed\jetified-play-services-auth-20.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\58daf85d66291f16c22e5db17fbb8b24\transformed\jetified-play-services-auth-api-phone-18.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-api-phone:18.0.2"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\58daf85d66291f16c22e5db17fbb8b24\transformed\jetified-play-services-auth-api-phone-18.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.recaptcha:recaptcha:18.6.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\772d5e5ffa60b06272bcc2af1d02beb4\transformed\jetified-recaptcha-18.6.1\jars\classes.jar"
      resolved="com.google.android.recaptcha:recaptcha:18.6.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\772d5e5ffa60b06272bcc2af1d02beb4\transformed\jetified-recaptcha-18.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.play:integrity:1.3.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\610b2577e38de70f50a069c0d38ec1ca\transformed\jetified-integrity-1.3.0\jars\classes.jar"
      resolved="com.google.android.play:integrity:1.3.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\610b2577e38de70f50a069c0d38ec1ca\transformed\jetified-integrity-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-database-collection:18.0.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\04ed46db81f5010929d06014123e1527\transformed\jetified-firebase-database-collection-18.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-database-collection:18.0.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\04ed46db81f5010929d06014123e1527\transformed\jetified-firebase-database-collection-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-appcheck-interop:17.1.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6e70edd2d9d1bb5fb78e5c0119bedc3e\transformed\jetified-firebase-appcheck-interop-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-appcheck-interop:17.1.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6e70edd2d9d1bb5fb78e5c0119bedc3e\transformed\jetified-firebase-appcheck-interop-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-fido:20.1.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\970c6c0319aa3d626d36e3c606ea73c6\transformed\jetified-play-services-fido-20.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-fido:20.1.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\970c6c0319aa3d626d36e3c606ea73c6\transformed\jetified-play-services-fido-20.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-base:18.0.4@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\75342560e33d0d9fb39b1f9db41d12e8\transformed\jetified-play-services-auth-base-18.0.4\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-base:18.0.4"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\75342560e33d0d9fb39b1f9db41d12e8\transformed\jetified-play-services-auth-base-18.0.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.1.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e15ee9b978911c075688ce6d8881a3da\transformed\jetified-play-services-base-18.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.1.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e15ee9b978911c075688ce6d8881a3da\transformed\jetified-play-services-base-18.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\af072110ddcdf7033053f37265a18958\transformed\jetified-firebase-iid-interop-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-iid-interop:17.1.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\af072110ddcdf7033053f37265a18958\transformed\jetified-firebase-iid-interop-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\bd4f52d394ba150007b160ff94ac2ed4\transformed\jetified-play-services-cloud-messaging-17.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-cloud-messaging:17.2.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\bd4f52d394ba150007b160ff94ac2ed4\transformed\jetified-play-services-cloud-messaging-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.7.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\f44ce256e9e41154b6e28ed0f8fc29c9\transformed\jetified-fragment-ktx-1.7.1\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.7.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\f44ce256e9e41154b6e28ed0f8fc29c9\transformed\jetified-fragment-ktx-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.9.3@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8864d15756d9956f0ea02681cd9b68e2\transformed\jetified-activity-ktx-1.9.3\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.9.3"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8864d15756d9956f0ea02681cd9b68e2\transformed\jetified-activity-ktx-1.9.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-stats:17.0.2@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\854e4947759a9237456e170d69678339\transformed\jetified-play-services-stats-17.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-stats:17.0.2"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\854e4947759a9237456e170d69678339\transformed\jetified-play-services-stats-17.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c992c2a2f371a25d051cb4152bfe134a\transformed\recyclerview-1.0.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c992c2a2f371a25d051cb4152bfe134a\transformed\recyclerview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c738f1e6a2d92bb12ecc59f77749984f\transformed\legacy-support-core-ui-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c738f1e6a2d92bb12ecc59f77749984f\transformed\legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\7b983c22f49259a72439137a1f2383eb\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\7b983c22f49259a72439137a1f2383eb\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\665cb6e27f24f2f72f04c4f619ccce78\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\665cb6e27f24f2f72f04c4f619ccce78\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\7efe8f72f09e48886d417f2d2efe1d01\transformed\jetified-lifecycle-runtime-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\7efe8f72f09e48886d417f2d2efe1d01\transformed\jetified-lifecycle-runtime-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\7fef2f99aefab4e42c2471e8f1778935\transformed\jetified-savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\7fef2f99aefab4e42c2471e8f1778935\transformed\jetified-savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2ab365fbb5481069e49f2072c82c67b8\transformed\slidingpanelayout-1.2.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.2.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2ab365fbb5481069e49f2072c82c67b8\transformed\slidingpanelayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-preferences-external-protobuf:1.1.3@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-external-protobuf\1.1.3\ddd0a2d64e3c928359c993d1291e535d5d7fc9a3\datastore-preferences-external-protobuf-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-external-protobuf:1.1.3"/>
  <library
      name="androidx.datastore:datastore-preferences-proto:1.1.3@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-proto\1.1.3\6d7430ed8d2b5f2b8675dad8d196ba5dd710921b\datastore-preferences-proto-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-proto:1.1.3"/>
  <library
      name="androidx.datastore:datastore-preferences-core-jvm:1.1.3@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\androidx.datastore\datastore-preferences-core-jvm\1.1.3\fb991f11389ccf2a5d5d4c99783ff958bc400\datastore-preferences-core-jvm-1.1.3.jar"
      resolved="androidx.datastore:datastore-preferences-core-jvm:1.1.3"/>
  <library
      name="androidx.datastore:datastore-core-okio-jvm:1.1.3@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\androidx.datastore\datastore-core-okio-jvm\1.1.3\ce08f132812044a9778547b299fd812e34dbd602\datastore-core-okio-jvm-1.1.3.jar"
      resolved="androidx.datastore:datastore-core-okio-jvm:1.1.3"/>
  <library
      name="androidx.datastore:datastore-core-android:1.1.3@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\f608c969ffb0a31f9f611b357c09c200\transformed\jetified-datastore-core-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-core-android:1.1.3"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\f608c969ffb0a31f9f611b357c09c200\transformed\jetified-datastore-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-preferences-android:1.1.3@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\dcfcd8aecea5b6ce18a053dec9463281\transformed\jetified-datastore-preferences-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-preferences-android:1.1.3"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\dcfcd8aecea5b6ce18a053dec9463281\transformed\jetified-datastore-preferences-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.datastore:datastore-android:1.1.3@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\00083b04b38c3c766b03f27df1c82002\transformed\jetified-datastore-release\jars\classes.jar"
      resolved="androidx.datastore:datastore-android:1.1.3"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\00083b04b38c3c766b03f27df1c82002\transformed\jetified-datastore-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-play-services\1.7.3\7087d47913cfb0062c9909dacbfc78fe44c5ecff\kotlinx-coroutines-play-services-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3"/>
  <library
      name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b32b788f899d0eb930512f05915272fc\transformed\jetified-firebase-installations-interop-17.1.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations-interop:17.1.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b32b788f899d0eb930512f05915272fc\transformed\jetified-firebase-installations-interop-17.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.2.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\164a276684d264af522ffc7cbda2051f\transformed\jetified-play-services-tasks-18.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.2.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\164a276684d264af522ffc7cbda2051f\transformed\jetified-play-services-tasks-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\bd13a8bff6aba1ae6748403c70f464d7\transformed\jetified-firebase-measurement-connector-19.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-measurement-connector:19.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\bd13a8bff6aba1ae6748403c70f464d7\transformed\jetified-firebase-measurement-connector-19.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.4.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2589dc72bfe363753ff7e766baa135ab\transformed\jetified-play-services-basement-18.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.4.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2589dc72bfe363753ff7e766baa135ab\transformed\jetified-play-services-basement-18.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.8.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e11b6da5f28ffd75bf66664d467589a8\transformed\browser-1.8.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.8.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\e11b6da5f28ffd75bf66664d467589a8\transformed\browser-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.1.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\12c5084d2eb6e209e8a5f2f0964b5df7\transformed\jetified-appcompat-resources-1.1.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.1.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\12c5084d2eb6e209e8a5f2f0964b5df7\transformed\jetified-appcompat-resources-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6513d07900c4ca104fbf80cae460f94a\transformed\drawerlayout-1.0.0\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6513d07900c4ca104fbf80cae460f94a\transformed\drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8503e403a02a8e3286710ffa396f3f6a\transformed\coordinatorlayout-1.0.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8503e403a02a8e3286710ffa396f3f6a\transformed\coordinatorlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.4.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\7b0f6745f3365da6ff3ad016a2511f6e\transformed\transition-1.4.1\jars\classes.jar"
      resolved="androidx.transition:transition:1.4.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\7b0f6745f3365da6ff3ad016a2511f6e\transformed\transition-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\217d23840cb8c3127fbf0615eb1d0827\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\217d23840cb8c3127fbf0615eb1d0827\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\bfcb191959d14eb63022a656149186f8\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\bfcb191959d14eb63022a656149186f8\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2680c79cccc8ad734689c94574f0d3ce\transformed\swiperefreshlayout-1.0.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\2680c79cccc8ad734689c94574f0d3ce\transformed\swiperefreshlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\661ecd22f46024ae173cb403b09ffc96\transformed\asynclayoutinflater-1.0.0\jars\classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\661ecd22f46024ae173cb403b09ffc96\transformed\asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6e0fe1122ed1d6fd2cab2fa53e61d9e3\transformed\localbroadcastmanager-1.1.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\6e0fe1122ed1d6fd2cab2fa53e61d9e3\transformed\localbroadcastmanager-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.7@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\64c6515632a4254ed66ef14de0b974d6\transformed\exifinterface-1.3.7\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.7"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\64c6515632a4254ed66ef14de0b974d6\transformed\exifinterface-1.3.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\ee3ce1c0aa5181f3c7fd002d716651f3\transformed\jetified-profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\d16c44b1768153149600cb80f691ab89\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\d16c44b1768153149600cb80f691ab89\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.1.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.1.0\f807b2f366f7b75142a67d2f3c10031065b5168\collection-ktx-1.1.0.jar"
      resolved="androidx.collection:collection-ktx:1.1.0"/>
  <library
      name="com.google.firebase:firebase-components:18.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\341493ff67939c6b07505a1291e5dd9a\transformed\jetified-firebase-components-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-components:18.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\341493ff67939c6b07505a1291e5dd9a\transformed\jetified-firebase-components-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="com.google.firebase:firebase-datatransport:18.2.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fd608e4250006cc300b2e0a8781b817c\transformed\jetified-firebase-datatransport-18.2.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-datatransport:18.2.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fd608e4250006cc300b2e0a8781b817c\transformed\jetified-firebase-datatransport-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-backend-cct:3.1.9@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b69b1306b3326e265f938d9f0333ec1e\transformed\jetified-transport-backend-cct-3.1.9\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-backend-cct:3.1.9"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b69b1306b3326e265f938d9f0333ec1e\transformed\jetified-transport-backend-cct-3.1.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0c709b0d530f51b688d05470992953d6\transformed\jetified-firebase-encoders-json-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-encoders-json:18.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0c709b0d530f51b688d05470992953d6\transformed\jetified-firebase-encoders-json-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-runtime:3.1.9@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a5d02eb0f48d1f354fd28ccb48986a7a\transformed\jetified-transport-runtime-3.1.9\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-runtime:3.1.9"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\a5d02eb0f48d1f354fd28ccb48986a7a\transformed\jetified-transport-runtime-3.1.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders-proto\16.0.0\a42d5fd83b96ae7b73a8617d29c94703e18c9992\firebase-encoders-proto-16.0.0.jar"
      resolved="com.google.firebase:firebase-encoders-proto:16.0.0"/>
  <library
      name="com.google.android.datatransport:transport-api:3.1.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fc6a4e34b3b5f0266262cc5872570724\transformed\jetified-transport-api-3.1.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-api:3.1.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\fc6a4e34b3b5f0266262cc5872570724\transformed\jetified-transport-api-3.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders:17.0.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders\17.0.0\26f52dc549c42575b155f8c720e84059ee600a85\firebase-encoders-17.0.0.jar"
      resolved="com.google.firebase:firebase-encoders:17.0.0"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b6bb59b7dc26df7b70a2c22eb7462e50\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\b6bb59b7dc26df7b70a2c22eb7462e50\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window.extensions.core:core:1.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4f80cba1b6927d4439c5d443256ac259\transformed\jetified-core-1.0.0\jars\classes.jar"
      resolved="androidx.window.extensions.core:core:1.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\4f80cba1b6927d4439c5d443256ac259\transformed\jetified-core-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\be640900a49eb8c6c7c14ddf291b73da\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\be640900a49eb8c6c7c14ddf291b73da\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8adc971fb2ee3a979b7bdafa7e34247d\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\8adc971fb2ee3a979b7bdafa7e34247d\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.libraries.identity.googleid:googleid:1.1.0@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\7e28f2eab9278c36845ede7866f6e0cf\transformed\jetified-googleid-1.1.0\jars\classes.jar"
      resolved="com.google.android.libraries.identity.googleid:googleid:1.1.0"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\7e28f2eab9278c36845ede7866f6e0cf\transformed\jetified-googleid-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.grpc:grpc-okhttp:1.62.2@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\io.grpc\grpc-okhttp\1.62.2\2d8b802e7fe17c1577527195fd53a2e7355b3541\grpc-okhttp-1.62.2.jar"
      resolved="io.grpc:grpc-okhttp:1.62.2"/>
  <library
      name="com.squareup.okio:okio-jvm:3.4.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.4.0\4e8bd78a52ab935ce383d0092646922154295e54\okio-jvm-3.4.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.4.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-parcelize-runtime\1.9.22\de4a21d6560cadd035c69ba3af3ad1afecc95299\kotlin-parcelize-runtime-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-android-extensions-runtime\1.9.22\ee3bc0c3b55cb516ac92d6a093e1b939166b86a2\kotlin-android-extensions-runtime-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22"/>
  <library
      name="io.grpc:grpc-android:1.62.2@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0cc0a1d33b69d349b3cc82fcb126506e\transformed\jetified-grpc-android-1.62.2\jars\classes.jar"
      resolved="io.grpc:grpc-android:1.62.2"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\0cc0a1d33b69d349b3cc82fcb126506e\transformed\jetified-grpc-android-1.62.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.grpc:grpc-util:1.62.2@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\io.grpc\grpc-util\1.62.2\98c4138f09fb57c3ad6cbeffb31ed73e302038f7\grpc-util-1.62.2.jar"
      resolved="io.grpc:grpc-util:1.62.2"/>
  <library
      name="io.grpc:grpc-core:1.62.2@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\io.grpc\grpc-core\1.62.2\5808049a5e33eba6f248a68d58e75399a68f2784\grpc-core-1.62.2.jar"
      resolved="io.grpc:grpc-core:1.62.2"/>
  <library
      name="com.google.code.gson:gson:2.10.1@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\com.google.code.gson\gson\2.10.1\b3add478d4382b78ea20b1671390a858002feb6c\gson-2.10.1.jar"
      resolved="com.google.code.gson:gson:2.10.1"/>
  <library
      name="io.grpc:grpc-protobuf-lite:1.62.2@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\io.grpc\grpc-protobuf-lite\1.62.2\9d807d2a0e34bd7284a5336186f57cf241090920\grpc-protobuf-lite-1.62.2.jar"
      resolved="io.grpc:grpc-protobuf-lite:1.62.2"/>
  <library
      name="io.grpc:grpc-stub:1.62.2@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\io.grpc\grpc-stub\1.62.2\fc1e85697502d96d6c912e8dd2a56f46f1aba050\grpc-stub-1.62.2.jar"
      resolved="io.grpc:grpc-stub:1.62.2"/>
  <library
      name="io.grpc:grpc-context:1.62.2@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\io.grpc\grpc-context\1.62.2\69e86c35140b3b1718d65635bb54ccecc4c12f14\grpc-context-1.62.2.jar"
      resolved="io.grpc:grpc-context:1.62.2"/>
  <library
      name="io.grpc:grpc-api:1.62.2@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\io.grpc\grpc-api\1.62.2\a93b6ee3761d48edd9a9279f20a58be1a245ad01\grpc-api-1.62.2.jar"
      resolved="io.grpc:grpc-api:1.62.2"/>
  <library
      name="com.google.guava:guava:32.1.3-android@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\com.google.guava\guava\32.1.3-android\ea090dd85ca2fa12d42d054369df888665230dd7\guava-32.1.3-android.jar"
      resolved="com.google.guava:guava:32.1.3-android"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="com.google.firebase:firebase-annotations:16.2.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\com.google.firebase\firebase-annotations\16.2.0\ba0806703ca285d03fa9c888b5868f101134a501\firebase-annotations-16.2.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.2.0"/>
  <library
      name="com.google.firebase:protolite-well-known-types:18.0.1@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\173eb89321ecfe81944086b5a743884c\transformed\jetified-protolite-well-known-types-18.0.1\jars\classes.jar"
      resolved="com.google.firebase:protolite-well-known-types:18.0.1"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\173eb89321ecfe81944086b5a743884c\transformed\jetified-protolite-well-known-types-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.26.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.26.0\c513866fd91bb46587500440a80fa943e95d12d9\error_prone_annotations-2.26.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.26.0"/>
  <library
      name="com.google.android.play:core-common:2.0.3@aar"
      jars="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c4b7ca6073ca4a730ec2a12d13ab7f10\transformed\jetified-core-common-2.0.3\jars\classes.jar"
      resolved="com.google.android.play:core-common:2.0.3"
      folder="C:\Program Files\Java\jdk-17\caches\8.11.1\transforms\c4b7ca6073ca4a730ec2a12d13ab7f10\transformed\jetified-core-common-2.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="com.google.protobuf:protobuf-javalite:3.25.5@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\com.google.protobuf\protobuf-javalite\3.25.5\272641fe157ed7c4a22f8d4c347bcd7f6eac8887\protobuf-javalite-3.25.5.jar"
      resolved="com.google.protobuf:protobuf-javalite:3.25.5"/>
  <library
      name="io.perfmark:perfmark-api:0.26.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\io.perfmark\perfmark-api\0.26.0\ef65452adaf20bf7d12ef55913aba24037b82738\perfmark-api-0.26.0.jar"
      resolved="io.perfmark:perfmark-api:0.26.0"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="com.google.android:annotations:4.1.1.4@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\com.google.android\annotations\4.1.1.4\a1678ba907bf92691d879fef34e1a187038f9259\annotations-4.1.1.4.jar"
      resolved="com.google.android:annotations:4.1.1.4"/>
  <library
      name="org.codehaus.mojo:animal-sniffer-annotations:1.23@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\org.codehaus.mojo\animal-sniffer-annotations\1.23\3c0daebd5f0e1ce72cc50c818321ac957aeb5d70\animal-sniffer-annotations-1.23.jar"
      resolved="org.codehaus.mojo:animal-sniffer-annotations:1.23"/>
  <library
      name="com.google.guava:failureaccess:1.0.1@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.1\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\failureaccess-1.0.1.jar"
      resolved="com.google.guava:failureaccess:1.0.1"/>
  <library
      name="org.checkerframework:checker-qual:3.37.0@jar"
      jars="C:\Program Files\Java\jdk-17\caches\modules-2\files-2.1\org.checkerframework\checker-qual\3.37.0\ba74746d38026581c12166e164bb3c15e90cc4ea\checker-qual-3.37.0.jar"
      resolved="org.checkerframework:checker-qual:3.37.0"/>
</libraries>
