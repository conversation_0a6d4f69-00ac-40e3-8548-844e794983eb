{"logs": [{"outputFile": "com.amalpoint.app-mergeDebugResources-51:/values-v22/values-v22.xml", "map": [{"source": "C:\\Program Files\\Java\\jdk-17\\caches\\8.11.1\\transforms\\87d519c141b7a1c36f4df3a53f4e9634\\transformed\\appcompat-1.1.0\\res\\values-v22\\values-v22.xml", "from": {"startLines": "2,3,4,9", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,553", "endLines": "2,3,8,13", "endColumns": "74,86,12,12", "endOffsets": "125,212,548,896"}, "to": {"startLines": "2,3,4,9", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,487", "endLines": "2,3,8,13", "endColumns": "74,86,12,12", "endOffsets": "125,212,482,764"}}]}]}