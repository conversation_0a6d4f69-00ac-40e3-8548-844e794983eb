import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../models/user_model.dart';
import '../models/product_model.dart';
import '../models/chat_model.dart';
import '../services/user_service.dart';
import '../services/product_service.dart';
import '../services/chat_service.dart';
import '../providers/auth_provider.dart';
import '../utils/role_access_control.dart';
import '../widgets/reseller_status_message.dart';
import 'user_profile_screen.dart';
import 'chat_screen.dart';
import 'reseller_registration_screen.dart';

class ResellerListScreen extends StatefulWidget {
  const ResellerListScreen({super.key});

  @override
  State<ResellerListScreen> createState() => _ResellerListScreenState();
}

class _ResellerListScreenState extends State<ResellerListScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  List<UserModel> _resellers = [];
  List<UserModel> _allResellers = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
    _loadResellers();
  }

  Future<void> _loadResellers() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      print('🔍 Loading resellers from Firebase...');
      final resellers = await UserService.getResellers();

      setState(() {
        _allResellers = resellers;
        _resellers = resellers;
        _isLoading = false;
      });
      print('✅ Successfully loaded ${resellers.length} resellers');
    } catch (e) {
      print('❌ Firebase Error: $e');

      // Extract and print index creation link
      String errorMessage = e.toString();
      if (errorMessage.contains('https://console.firebase.google.com')) {
        RegExp linkRegex = RegExp(r'https://console\.firebase\.google\.com[^\s\]]+');
        Match? match = linkRegex.firstMatch(errorMessage);
        if (match != null) {
          String indexLink = match.group(0)!;
          print('\n🔗 FIREBASE INDEX CREATION LINK:');
          print(indexLink);
          print('\n📋 Copy this link and paste in browser to create the index.');
        }
      }

      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _searchResellers() async {
    if (_searchQuery.isEmpty) {
      setState(() {
        _resellers = _allResellers;
      });
      return;
    }

    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      print('🔍 Searching resellers with query: "$_searchQuery"');

      // Local search first for better performance
      final localResults = _allResellers.where((reseller) {
        final searchLower = _searchQuery.toLowerCase();
        return reseller.displayName.toLowerCase().contains(searchLower) ||
               (reseller.bio?.toLowerCase().contains(searchLower) ?? false) ||
               (reseller.address?.toLowerCase().contains(searchLower) ?? false);
      }).toList();

      setState(() {
        _resellers = localResults;
        _isLoading = false;
      });

      print('✅ Search completed. Found ${localResults.length} resellers');
    } catch (e) {
      print('❌ Search Error: $e');
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  List<UserModel> get filteredResellers {
    return _resellers;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF667eea),
              Color(0xFF764ba2),
            ],
          ),
        ),
        child: SafeArea(
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Column(
                    children: [
                      _buildHeader(),
                      Expanded(
                        child: _buildResellerContent(),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: IconButton(
                  icon: const Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                  ),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
              const Spacer(),
              Text(
                'Resellers',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildResellerContent() {
    final authProvider = Provider.of<AuthProvider>(context);
    final currentUser = authProvider.currentUser;

    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(30),
          topRight: Radius.circular(30),
        ),
      ),
      child: Column(
        children: [
          const SizedBox(height: AppConstants.paddingLarge),

          // Show reseller application status message
          if (currentUser != null && !currentUser.resellerApplicationStatus.isNone)
            ResellerStatusMessage(
              status: currentUser.resellerApplicationStatus,
              messageKey: 'reseller_status_${currentUser.id}',
            ),

          _buildSearchBar(),
          const SizedBox(height: AppConstants.paddingLarge),
          _buildResellerRegistrationButton(),
          Expanded(
            child: _buildResellerList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: TextField(
          controller: _searchController,
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
            _searchResellers();
          },
          decoration: InputDecoration(
            hintText: 'Search resellers...',
            prefixIcon: Icon(
              Icons.search,
              color: AppConstants.primaryColor.withOpacity(0.7),
            ),
            suffixIcon: _searchQuery.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      _searchController.clear();
                      setState(() {
                        _searchQuery = '';
                        _resellers = _allResellers;
                      });
                    },
                  )
                : null,
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
              vertical: AppConstants.paddingMedium,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildResellerRegistrationButton() {
    final authProvider = Provider.of<AuthProvider>(context);
    final currentUser = authProvider.currentUser;

    // Don't show button if user is null, can't apply for reseller, or already has pending/approved application
    if (currentUser == null ||
        !RoleAccessControl.canApplyForReseller(currentUser) ||
        currentUser.resellerApplicationStatus.isPending ||
        currentUser.resellerApplicationStatus.isApproved) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
      child: Container(
        width: double.infinity,
        margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
        child: ElevatedButton.icon(
          onPressed: () => _navigateToResellerRegistration(),
          icon: const Icon(Icons.store),
          label: const Text('Reseller Registration'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppConstants.primaryColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 14),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            ),
            elevation: 2,
          ),
        ),
      ),
    );
  }

  Widget _buildResellerList() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppConstants.errorColor.withOpacity(0.5),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              'Error loading resellers',
              style: TextStyle(
                color: AppConstants.errorColor,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              _errorMessage!,
              style: TextStyle(
                color: AppConstants.textSecondaryColor.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            ElevatedButton(
              onPressed: _loadResellers,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    final resellers = filteredResellers;

    if (resellers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: AppConstants.textSecondaryColor.withOpacity(0.5),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              'No resellers found',
              style: TextStyle(
                color: AppConstants.textSecondaryColor,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              'Try adjusting your search',
              style: TextStyle(
                color: AppConstants.textSecondaryColor.withOpacity(0.7),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
      itemCount: resellers.length,
      itemBuilder: (context, index) {
        return _buildResellerCard(resellers[index]);
      },
    );
  }

  Widget _buildResellerCard(UserModel reseller) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: AppConstants.paddingSmall / 2,
      ),
      child: Card(
        elevation: 2,
        shadowColor: Colors.black.withOpacity(0.05),
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: AppConstants.borderColor,
            width: 1,
          ),
        ),
        child: Container(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Row(
            children: [
              // Profile Picture
              CircleAvatar(
                radius: 30,
                backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
                backgroundImage: reseller.profileImageUrl != null
                    ? NetworkImage(reseller.profileImageUrl!)
                    : null,
                child: reseller.profileImageUrl == null
                    ? Text(
                        reseller.displayName.substring(0, 1).toUpperCase(),
                        style: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: AppConstants.primaryColor,
                        ),
                      )
                    : null,
              ),

              const SizedBox(width: 16),

              // Reseller Info - Only Name
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Name
                    Text(
                      reseller.displayName,
                      style: const TextStyle(
                        fontSize: 17,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 6),

                    // Verified Badge
                    if (reseller.isVerified)
                      Row(
                        children: [
                          Icon(
                            Icons.verified,
                            size: 16,
                            color: AppConstants.successColor,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Verified Reseller',
                            style: TextStyle(
                              fontSize: 12,
                              color: AppConstants.successColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }



  void _showResellerDetails(UserModel reseller) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.9,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Expanded(
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(AppConstants.paddingLarge),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 40,
                          backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
                          backgroundImage: reseller.profileImageUrl != null
                              ? NetworkImage(reseller.profileImageUrl!)
                              : null,
                          child: reseller.profileImageUrl == null
                              ? Text(
                                  reseller.displayName.substring(0, 1).toUpperCase(),
                                  style: const TextStyle(
                                    fontSize: 32,
                                    fontWeight: FontWeight.bold,
                                    color: AppConstants.primaryColor,
                                  ),
                                )
                              : null,
                        ),
                        const SizedBox(width: AppConstants.paddingMedium),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                reseller.displayName,
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Icon(Icons.person, color: AppConstants.primaryColor, size: 16),
                                  const SizedBox(width: 4),
                                  Text('Reseller'),
                                ],
                              ),
                            ],
                          ),
                        ),
                        if (reseller.isVerified)
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: AppConstants.successColor.withOpacity(0.1),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.verified,
                              color: AppConstants.successColor,
                              size: 20,
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: AppConstants.paddingLarge),
                    Text(
                      'About',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppConstants.paddingSmall),
                    Text(reseller.bio ?? 'No bio available'),
                    const SizedBox(height: AppConstants.paddingMedium),
                    Text(
                      'Location',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppConstants.paddingSmall),
                    Row(
                      children: [
                        Icon(Icons.location_on, color: AppConstants.primaryColor, size: 16),
                        const SizedBox(width: 4),
                        Text(reseller.address ?? 'Location not specified'),
                      ],
                    ),
                    const SizedBox(height: AppConstants.paddingMedium),
                    Text(
                      'Member Since',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppConstants.paddingSmall),
                    Text('${reseller.createdAt.day}/${reseller.createdAt.month}/${reseller.createdAt.year}'),
                    const SizedBox(height: AppConstants.paddingLarge),

                    // Products Section
                    Text(
                      'Products',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppConstants.paddingMedium),
                    _buildResellerProducts(reseller.id),

                        ],
                      ),
                    ),
                  ),
                  // Fixed Button Section
                  Container(
                    padding: const EdgeInsets.all(AppConstants.paddingLarge),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, -2),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: () => _contactReseller(reseller),
                            icon: const Icon(Icons.message),
                            label: const Text('Contact'),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              side: BorderSide(color: AppConstants.primaryColor),
                            ),
                          ),
                        ),
                        const SizedBox(width: AppConstants.paddingMedium),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () => _viewProducts(reseller),
                            icon: const Icon(Icons.shopping_bag),
                            label: const Text('View Profile'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppConstants.primaryColor,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResellerProducts(String resellerId) {
    return FutureBuilder<List<ProductModel>>(
      future: ProductService.getProductsByReseller(resellerId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(AppConstants.paddingMedium),
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (snapshot.hasError) {
          return Center(
            child: Text(
              'Error loading products',
              style: TextStyle(color: AppConstants.errorColor),
            ),
          );
        }

        final products = snapshot.data ?? [];

        if (products.isEmpty) {
          return Container(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            ),
            child: Center(
              child: Text(
                'No products available',
                style: TextStyle(
                  color: AppConstants.textSecondaryColor,
                  fontSize: 14,
                ),
              ),
            ),
          );
        }

        return Column(
          children: [
            Container(
              height: 500,
              child: GridView.builder(
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 0.65, // Adjusted to make cards taller
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                ),
                itemCount: products.length > 4 ? 4 : products.length,
                itemBuilder: (context, index) {
              final product = products[index];
              return Card(
                elevation: 12,
                shadowColor: Colors.black.withOpacity(0.25),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () => _onProductTap(product),
                    borderRadius: BorderRadius.circular(16),
                    splashColor: AppConstants.primaryColor.withOpacity(0.1),
                    highlightColor: AppConstants.primaryColor.withOpacity(0.05),
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.15),
                            blurRadius: 12,
                            offset: const Offset(0, 6),
                          ),
                          BoxShadow(
                            color: Colors.black.withOpacity(0.08),
                            blurRadius: 24,
                            offset: const Offset(0, 12),
                          ),
                          BoxShadow(
                            color: AppConstants.primaryColor.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                    Expanded(
                      flex: 3,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.grey.withOpacity(0.05),
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(16),
                            topRight: Radius.circular(16),
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: product.imageUrls.isNotEmpty
                            ? Container(
                                decoration: const BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(16),
                                    topRight: Radius.circular(16),
                                  ),
                                ),
                                child: ClipRRect(
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(16),
                                    topRight: Radius.circular(16),
                                  ),
                                  child: Image.network(
                                    product.imageUrls.first,
                                    fit: BoxFit.contain,
                                    width: double.infinity,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors: [
                                            Colors.grey.shade200,
                                            Colors.grey.shade300,
                                          ],
                                        ),
                                      ),
                                      child: const Center(
                                        child: Icon(
                                          Icons.image_not_supported,
                                          color: Colors.grey,
                                          size: 32,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            )
                            : Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      AppConstants.primaryColor.withOpacity(0.1),
                                      AppConstants.primaryColor.withOpacity(0.2),
                                    ],
                                  ),
                                ),
                                child: Center(
                                  child: Icon(
                                    Icons.shopping_bag_outlined,
                                    size: 40,
                                    color: AppConstants.primaryColor,
                                  ),
                                ),
                              ),
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              product.name,
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const Spacer(),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 10,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    AppConstants.primaryColor.withOpacity(0.1),
                                    AppConstants.primaryColor.withOpacity(0.15),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                  color: AppConstants.primaryColor.withOpacity(0.2),
                                  width: 1,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppConstants.primaryColor.withOpacity(0.1),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Text(
                                '৳${product.price.toStringAsFixed(0)}',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: AppConstants.primaryColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                      ],
                    ),
                  ),
                  ),
                ),
              );
            },
          ),
        ),
        if (products.length > 4)
          const SizedBox(height: 16),
        if (products.length > 4)
          Container(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _viewAllProducts(resellerId),
              icon: Icon(
                Icons.shopping_bag_outlined,
                size: 18,
                color: AppConstants.primaryColor,
              ),
              label: Text(
                'View All Products (${products.length})',
                style: TextStyle(
                  color: AppConstants.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                side: BorderSide(
                  color: AppConstants.primaryColor,
                  width: 1.5,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      );
      },
    );
  }

  void _onProductTap(ProductModel product) {
    // Navigate to product details screen
    Navigator.pushNamed(
      context,
      '/product-details',
      arguments: {
        'productId': product.id,
        'product': product,
      },
    );
  }

  void _viewAllProducts(String resellerId) async {
    try {
      // Get reseller user data
      final reseller = await UserService.getUserById(resellerId);

      if (reseller != null) {
        // Navigate to user profile screen
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => UserProfileScreen(
              userId: reseller.id,
              initialUserName: reseller.displayName,
            ),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('User not found'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    }
  }

  void _contactReseller(UserModel reseller) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please login to start a chat'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
      return;
    }

    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      // Create or get existing chat
      final chat = await ChatService.createOrGetChat(
        currentUserId: currentUser.id,
        otherUserId: reseller.id,
        currentUserName: currentUser.displayName,
        otherUserName: reseller.displayName,
      );

      // Hide loading dialog
      Navigator.of(context).pop();

      if (chat != null) {
        // Navigate to chat conversation
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ChatConversationScreen(
              chat: chat,
              otherUser: reseller,
            ),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to start chat. Please try again.'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } catch (e) {
      // Hide loading dialog
      Navigator.of(context).pop();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error starting chat: ${e.toString()}'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    }
  }

  void _viewProducts(UserModel reseller) {
    // Navigate to user profile screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UserProfileScreen(
          userId: reseller.id,
          initialUserName: reseller.displayName,
        ),
      ),
    );
  }

  void _navigateToResellerRegistration() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ResellerRegistrationScreen(),
      ),
    );
  }
}


